<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>
        5 Star Lettings & Sales | Premium Property Services in Blackburn
    </title>
    <meta name="description" content="Premium property sales for just £499 flat fee and property management at 8% with free maintenance. Blackburn's luxury property specialists." />
    <link href="css/tailwind.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="css/all.min.css" />
    <link rel="stylesheet" href="css/aos.css" />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin="" />
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700&family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet" />
    <style>
        :root {
            --gold: #d4af37;
            --gold-light: #f5e7a9;
            --gold-dark: #a67c00;
            --black: #111111;
        }

        body {
            font-family: "Poppins", sans-serif;
            scroll-behavior: smooth;
        }

        .font-playfair {
            font-family: "Playfair Display", serif;
        }

        .gold-gradient {
            background: linear-gradient( 135deg, var(--gold-light), var(--gold), var(--gold-dark));
        }

        .text-gold {
            color: var(--gold);
        }

        .gold-border {
            border-color: var(--gold);
        }

        .gold-shadow {
            box-shadow: 0 4px 20px rgba(212, 175, 55, 0.3);
        }

        .glass-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .gradient-border {
            position: relative;
            border: 2px solid transparent;
            background-clip: padding-box;
        }

        .gradient-border::before {
            content: "";
            position: absolute;
            top: 0;
            right: 0;
            bottom: 0;
            left: 0;
            margin: -2px;
            border-radius: inherit;
            background: linear-gradient( to right, var(--gold-light), var(--gold), var(--gold-dark));
            z-index: -1;
        }

        .hero {
            background-image: url("https://images.unsplash.com/photo-1600585154340-be6161a56a0c?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1920&q=80");
            background-size: cover;
            background-position: center;
        }

        .hero::before {
            content: "";
            position: absolute;
            top: 0;
            right: 0;
            bottom: 0;
            left: 0;
            background: linear-gradient( to bottom, rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.5));
        }
    </style>
</head>

<body class="antialiased bg-black text-white">
    <!-- Header/Navigation -->
    <header class="fixed w-full z-50 transition-all duration-300" id="navbar">
        <div class="container mx-auto px-5 py-3">
            <div class="flex justify-between items-center">
                <a href="#" class="flex items-center space-x-2">
                    <div class="h-14 w-auto bg-black rounded-lg p-1">
                        <img src="images/Star Letting.png" alt="5 Star Lettings & Sales" class="h-full" />
                    </div>
                </a>

                <nav class="hidden md:flex space-x-8 items-center">
                    <a href="#home" class="text-white hover:text-gold transition duration-300 font-medium">Home</a
            >
            <a
              href="#services"
              class="text-white hover:text-gold transition duration-300 font-medium"
              >Services</a
            >
            <a
              href="#about"
              class="text-white hover:text-gold transition duration-300 font-medium"
              >About Us</a
            >
            <a
              href="#properties"
              class="text-white hover:text-gold transition duration-300 font-medium"
              >Properties</a
            >
            <a
              href="#testimonials"
              class="text-white hover:text-gold transition duration-300 font-medium"
              >Testimonials</a
            >
            <a
              href="#contact"
              class="text-white hover:text-gold transition duration-300 font-medium"
              >Contact</a
            >
            <a
              href="#valuation"
              class="gradient-border px-5 py-2 rounded-full text-white hover:text-gold-light transition duration-300 font-medium"
              >Free Valuation</a
            >
          </nav>

          <button class="md:hidden text-gold text-2xl" id="mobile-menu-button">
            <i class="fas fa-bars"></i>
          </button>
        </div>
      </div>

      <!-- Mobile menu -->
      <div
        class="md:hidden hidden glass-card mt-3 mx-5 rounded-xl p-5 text-center"
        id="mobile-menu"
      >
        <div class="flex flex-col space-y-4">
          <a
            href="#home"
            class="text-white hover:text-gold transition duration-300 font-medium"
            >Home</a
          >
          <a
            href="#services"
            class="text-white hover:text-gold transition duration-300 font-medium"
            >Services</a
          >
          <a
            href="#about"
            class="text-white hover:text-gold transition duration-300 font-medium"
            >About Us</a
          >
          <a
            href="#properties"
            class="text-white hover:text-gold transition duration-300 font-medium"
            >Properties</a
          >
          <a
            href="#testimonials"
            class="text-white hover:text-gold transition duration-300 font-medium"
            >Testimonials</a
          >
          <a
            href="#contact"
            class="text-white hover:text-gold transition duration-300 font-medium"
            >Contact</a
          >
          <a
            href="#valuation"
            class="gold-gradient px-5 py-2 rounded-full text-black hover:opacity-90 transition duration-300 font-medium"
            >Free Valuation</a
          >
        </div>
      </div>
    </header>

    <!-- Hero Section -->
    <section id="home" class="hero relative min-h-screen flex items-center">
      <div class="container mx-auto px-5 py-20 relative z-10">
        <div class="max-w-3xl" data-aos="fade-right" data-aos-duration="1000">
          <h1 class="text-4xl md:text-6xl font-bold mb-6 font-playfair">
            <span class="text-white">Exceptional Property</span>
            <span class="block text-gold">Services in Blackburn</span>
          </h1>
          <p class="text-xl text-gray-200 mb-8">
            We are estate agents and letting agents covering Blackburn, Darwen, Preston and Accrington.
            Experience premium property services with unbeatable value. Flat fee
            sales and comprehensive management solutions, 
          </p>
          <div
            class="flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4"
          >
            <a
              href="#valuation"
              class="gold-gradient text-center px-8 py-4 rounded-full text-black font-semibold transition duration-300 hover:opacity-90 transform hover:-translate-y-1"
            >
              Request Free Valuation
            </a>
                    <a href="#contact" class="gradient-border bg-transparent text-center px-8 py-4 rounded-full text-white font-semibold transition duration-300 hover:text-gold transform hover:-translate-y-1">
              Contact Us
            </a>
            </div>
        </div>
        </div>

        <!-- Floating stats cards -->
        <div class="absolute bottom-20 right-10 lg:right-20 max-w-lg hidden lg:block" data-aos="fade-left" data-aos-duration="1200">
            <div class="grid grid-cols-2 gap-5">
                <div class="glass-card p-6 rounded-xl gold-shadow">
                    <div class="text-gold text-4xl mb-2">£499</div>
                    <div class="text-white font-medium">Flat Fee Sales</div>
                    <div class="text-gray-300 text-sm">No hidden charges</div>
                </div>
                <div class="glass-card p-6 rounded-xl gold-shadow">
                    <div class="text-gold text-4xl mb-2">8%</div>
                    <div class="text-white font-medium">Management Fee</div>
                    <div class="text-gray-300 text-sm">Including free maintenance</div>
                </div>
            </div>
        </div>
        </section>

        <!-- Services Section -->
        <section id="services" class="py-20 bg-gradient-to-b from-black to-gray-900">
            <div class="container mx-auto px-5">
                <div class="text-center mb-16" data-aos="fade-up">
                    <h2 class="text-3xl md:text-5xl font-bold mb-4 font-playfair">
                        <span class="text-gold">Premium</span> Services
                    </h2>
                    <p class="text-xl text-gray-300 max-w-3xl mx-auto">
                        Tailored solutions for property owners with exceptional value and uncompromising quality
                    </p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-10">
                    <!-- Sales Service -->
                    <div class="glass-card p-8 rounded-2xl transition-all duration-300 hover:gold-shadow" data-aos="fade-up" data-aos-delay="100">
                        <div class="text-gold text-5xl mb-6">
                            <i class="fas fa-home"></i>
                        </div>
                        <h3 class="text-2xl font-bold mb-4 font-playfair">
                            Property Sales
                        </h3>
                        <div class="flex items-baseline mb-6">
                            <span class="text-gold text-4xl font-bold">£499</span>
                            <span class="text-gray-300 ml-2">flat fee</span>
                        </div>
                        <ul class="space-y-3 mb-8">
                            <li class="flex items-start">
                                <i class="fas fa-check text-gold mt-1 mr-3"></i>
                                <span>No hidden charges or commission</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-check text-gold mt-1 mr-3"></i>
                                <span>Professional photography included</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-check text-gold mt-1 mr-3"></i>
                                <span>Featured on major property portals</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-check text-gold mt-1 mr-3"></i>
                                <span>Dedicated sales consultant</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-check text-gold mt-1 mr-3"></i>
                                <span>Comprehensive marketing strategy</span>
                            </li>
                        </ul>
                        <a href="#contact" class="gradient-border block text-center py-3 rounded-xl text-white hover:text-gold-light transition duration-300">
              Sell Your Property
            </a>
                        <p class="flex items-center" style="margin-top: 10px">
                            <span class="mr-1">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  fill="white"
                  viewBox="0 0 16 16"
                >
                  <path
                    d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z"
                  />
                  <path
                    d="M8.93 6.588l-2.29.287-.082.38.45.083c.294.07.352.176.288.469l-.738 3.468c-.194.897.105 1.319.808 1.319.545 0 1.178-.252 1.465-.598l.088-.416c-.2.176-.492.246-.686.246-.275 0-.375-.193-.304-.533L8.93 6.588zM9 4.5a1 1 0 1 1-2 0 1 1 0 0 1 2 0z"
                  />
                </svg>
              </span>
                            <span style="font-size: 9px">Terms And Conditions Apply</span>
                        </p>
                    </div>

                    <!-- Lettings Service -->
                    <div class="glass-card p-8 rounded-2xl transition-all duration-300 hover:gold-shadow" data-aos="fade-up" data-aos-delay="200">
                        <div class="text-gold text-5xl mb-6">
                            <i class="fas fa-key"></i>
                        </div>
                        <h3 class="text-2xl font-bold mb-4 font-playfair">
                            Property Management
                        </h3>
                        <div class="flex items-baseline mb-6">
                            <span class="text-gold text-4xl font-bold">8%</span>
                            <span class="text-gray-300 ml-2">management fee</span>
                        </div>
                        <ul class="space-y-3 mb-8">
                            <li class="flex items-start">
                                <i class="fas fa-check text-gold mt-1 mr-3"></i>
                                <span>Free maintenance service included</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-check text-gold mt-1 mr-3"></i>
                                <span>Tenant finding and vetting</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-check text-gold mt-1 mr-3"></i>
                                <span>Regular property inspections</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-check text-gold mt-1 mr-3"></i>
                                <span>Rent collection and remittance</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-check text-gold mt-1 mr-3"></i>
                                <span>24/7 emergency contact</span>
                            </li>
                        </ul>
                        <a href="#contact" class="gradient-border block text-center py-3 rounded-xl text-white hover:text-gold-light transition duration-300">
              Let Your Property
            </a>
                        <p class="flex items-center" style="margin-top: 10px">
                            <span class="mr-1">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  fill="white"
                  viewBox="0 0 16 16"
                >
                  <path
                    d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z"
                  />
                  <path
                    d="M8.93 6.588l-2.29.287-.082.38.45.083c.294.07.352.176.288.469l-.738 3.468c-.194.897.105 1.319.808 1.319.545 0 1.178-.252 1.465-.598l.088-.416c-.2.176-.492.246-.686.246-.275 0-.375-.193-.304-.533L8.93 6.588zM9 4.5a1 1 0 1 1-2 0 1 1 0 0 1 2 0z"
                  />
                </svg>
              </span>
                            <span style="font-size: 9px">Terms And Conditions Apply</span>
                        </p>
                    </div>
                </div>

                <!-- Additional Service Highlights -->
                <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mt-16">
                    <div class="p-6 text-center" data-aos="fade-up" data-aos-delay="100">
                        <div class="text-gold text-4xl mb-4">
                            <i class="fas fa-star"></i>
                        </div>
                        <h4 class="text-xl font-semibold mb-2">Premium Marketing</h4>
                        <p class="text-gray-400">
                            High-quality photography and premium listings
                        </p>
                    </div>
                    <div class="p-6 text-center" data-aos="fade-up" data-aos-delay="200">
                        <div class="text-gold text-4xl mb-4">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <h4 class="text-xl font-semibold mb-2">Full Protection</h4>
                        <p class="text-gray-400">
                            Comprehensive landlord and tenant protection
                        </p>
                    </div>
                    <div class="p-6 text-center" data-aos="fade-up" data-aos-delay="300">
                        <div class="text-gold text-4xl mb-4">
                            <i class="fas fa-hand-holding-heart"></i>
                        </div>
                        <h4 class="text-xl font-semibold mb-2">Personalized Service</h4>
                        <p class="text-gray-400">Dedicated agent for your property needs</p>
                    </div>
                    <div class="p-6 text-center" data-aos="fade-up" data-aos-delay="400">
                        <div class="text-gold text-4xl mb-4">
                            <i class="fas fa-pound-sign"></i>
                        </div>
                        <h4 class="text-xl font-semibold mb-2">Competitive Pricing</h4>
                        <p class="text-gray-400">Best value services in Blackburn</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- About Section -->
        <section id="about" class="py-20 bg-black">
            <div class="container mx-auto px-5">
                <div class="flex flex-col lg:flex-row items-center gap-12">
                    <div class="lg:w-1/2" data-aos="fade-right">
                        <h2 class="text-3xl md:text-5xl font-bold mb-6 font-playfair">
                            <span class="text-white">About </span>
                            <span class="text-gold">5 Star Lettings & Sales</span>
                        </h2>
                        <p class="text-lg text-gray-300 mb-6">
                            Established in Blackburn, 5 Star Lettings & Sales has built a reputation for excellence in the property market. We combine local expertise with innovative solutions to deliver exceptional results for our clients.
                        </p>
                        <p class="text-lg text-gray-300 mb-6">
                            Our team of experienced professionals are dedicated to providing a personalized service that exceeds expectations. Whether you're selling, buying, letting, or renting, we offer unparalleled support throughout the entire process.
                        </p>
                        <p class="text-lg text-gray-300 mb-8">
                            With our competitive flat fee for sales and comprehensive management service, we provide premium solutions at accessible prices, ensuring excellent value without compromising on quality.
                        </p>
                        <div class="flex space-x-6">
                            <div>
                                <div class="text-gold text-4xl font-bold">150+</div>
                                <div class="text-gray-400">Properties Sold</div>
                            </div>
                            <div>
                                <div class="text-gold text-4xl font-bold">200+</div>
                                <div class="text-gray-400">Managed Properties</div>
                            </div>
                            <div>
                                <div class="text-gold text-4xl font-bold">98%</div>
                                <div class="text-gray-400">Client Satisfaction</div>
                            </div>
                        </div>
                    </div>
                    <div class="lg:w-1/2 relative" data-aos="fade-left">
                        <div class="relative z-10 rounded-2xl overflow-hidden gold-shadow">
                            <img src="https://images.unsplash.com/photo-1560518883-ce09059eeffa?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=800&q=80" alt="5 Star Lettings & Sales Office" class="w-full h-full object-cover" />
                        </div>
                        <div class="absolute -bottom-6 -right-6 w-48 h-48 gold-gradient rounded-2xl -z-10"></div>
                        <div class="absolute -top-6 -left-6 w-32 h-32 gold-gradient rounded-2xl opacity-50 -z-10"></div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Testimonials -->
        <section id="testimonials" class="py-20 bg-black">
            <div class="container mx-auto px-5">
                <div class="text-center mb-16" data-aos="fade-up">
                    <h2 class="text-3xl md:text-5xl font-bold mb-4 font-playfair">
                        <span class="text-white">Client </span>
                        <span class="text-gold">Testimonials</span>
                    </h2>
                    <p class="text-xl text-gray-300 max-w-3xl mx-auto">
                        Hear what our satisfied clients have to say about our services
                    </p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                    <!-- Testimonial 1 -->
                    <div class="glass-card p-8 rounded-2xl transition-all duration-300 hover:gold-shadow" data-aos="fade-up" data-aos-delay="100">
                        <div class="text-gold text-3xl mb-6">
                            <i class="fas fa-quote-left"></i>
                        </div>
                        <p class="text-gray-300 mb-6">
                            "5 Star Lettings & Sales sold my house in just 3 weeks for the full asking price. Their flat fee saved me thousands compared to traditional estate agents. Highly recommended!"
                        </p>
                        <div class="flex items-center">
                            <div class="w-12 h-12 bg-gold rounded-full flex items-center justify-center text-black font-bold mr-4">
                                JD
                            </div>
                            <div>
                                <h4 class="font-bold">John Davies</h4>
                                <p class="text-gray-400 text-sm">Property Seller</p>
                            </div>
                        </div>
                        <div class="mt-4 text-gold">
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                        </div>
                    </div>

                    <!-- Testimonial 2 -->
                    <div class="glass-card p-8 rounded-2xl transition-all duration-300 hover:gold-shadow" data-aos="fade-up" data-aos-delay="200">
                        <div class="text-gold text-3xl mb-6">
                            <i class="fas fa-quote-left"></i>
                        </div>
                        <p class="text-gray-300 mb-6">
                            "As a landlord, I've been using their management service for 2 years. The 8% fee with free maintenance has saved me both money and stress. They handle everything professionally."
                        </p>
                        <div class="flex items-center">
                            <div class="w-12 h-12 bg-gold rounded-full flex items-center justify-center text-black font-bold mr-4">
                                SA
                            </div>
                            <div>
                                <h4 class="font-bold">Sarah Ahmed</h4>
                                <p class="text-gray-400 text-sm">Landlord</p>
                            </div>
                        </div>
                        <div class="mt-4 text-gold">
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                        </div>
                    </div>

                    <!-- Testimonial 3 -->
                    <div class="glass-card p-8 rounded-2xl transition-all duration-300 hover:gold-shadow" data-aos="fade-up" data-aos-delay="300">
                        <div class="text-gold text-3xl mb-6">
                            <i class="fas fa-quote-left"></i>
                        </div>
                        <p class="text-gray-300 mb-6">
                            "We found our dream home through 5 Star Lettings & Sales. Their team was attentive to our requirements and made the whole buying process smooth and stress-free."
                        </p>
                        <div class="flex items-center">
                            <div class="w-12 h-12 bg-gold rounded-full flex items-center justify-center text-black font-bold mr-4">
                                MT
                            </div>
                            <div>
                                <h4 class="font-bold">Michael & Tina Thompson</h4>
                                <p class="text-gray-400 text-sm">Property Buyers</p>
                            </div>
                        </div>
                        <div class="mt-4 text-gold">
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Free Valuation -->
        <section id="valuation" class="py-20 bg-gradient-to-b from-black to-gray-900 relative overflow-hidden">
            <div class="absolute top-0 right-0 w-1/2 h-full bg-gold opacity-10 -skew-x-12 transform translate-x-1/4"></div>
            <div class="container mx-auto px-5 relative z-10">
                <div class="max-w-4xl mx-auto">
                    <div class="glass-card p-10 rounded-3xl gold-shadow" data-aos="zoom-in">
                        <div class="text-center mb-8">
                            <h2 class="text-3xl md:text-4xl font-bold mb-4 font-playfair">
                                <span class="text-gold">Free Property Valuation</span>
                            </h2>
                            <p class="text-lg text-gray-300">
                                Curious about your property's worth? Request a free, no-obligation valuation from our experts.
                            </p>
                        </div>

                        <form class="space-y-6" action="https://submit-form.com/b36d4cPGL">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label class="block text-gray-300 mb-2">Full Name</label>
                                    <input type="text" class="w-full bg-black bg-opacity-50 border border-gray-700 rounded-lg px-4 py-3 text-white focus:border-gold focus:outline-none transition duration-300" placeholder="Your Name" name="Name" />
                                </div>
                                <div>
                                    <label class="block text-gray-300 mb-2">Phone Number</label>
                                    <input type="tel" class="w-full bg-black bg-opacity-50 border border-gray-700 rounded-lg px-4 py-3 text-white focus:border-gold focus:outline-none transition duration-300" placeholder="Your Phone" name="Phone" />
                                </div>
                                <div>
                                    <label class="block text-gray-300 mb-2">Email Address</label>
                                    <input type="email" class="w-full bg-black bg-opacity-50 border border-gray-700 rounded-lg px-4 py-3 text-white focus:border-gold focus:outline-none transition duration-300" placeholder="Your Email" name="Email" />
                                </div>
                                <div>
                                    <label class="block text-gray-300 mb-2">Property Postcode</label
                  >
                  <input
                    type="text"
                    class="w-full bg-black bg-opacity-50 border border-gray-700 rounded-lg px-4 py-3 text-white focus:border-gold focus:outline-none transition duration-300"
                    placeholder="Property Postcode"
                    name="Postal Code"
                  />
                </div>
              </div>

              <div>
                <label class="block text-gray-300 mb-2">Property Type</label>
                                    <select name="Property Type" class="w-full bg-black bg-opacity-50 border border-gray-700 rounded-lg px-4 py-3 text-white focus:border-gold focus:outline-none transition duration-300">
                  <option>Select Property Type</option>
                  <option>Detached House</option>
                  <option>Semi-Detached House</option>
                  <option>Terraced House</option>
                  <option>Flat/Apartment</option>
                  <option>Bungalow</option>
                  <option>Commercial Property</option>
                  <option>Other</option>
                </select>
                                </div>

                                <div>
                                    <label class="block text-gray-300 mb-2">Additional Information</label
                >
                <textarea name="Other Info"
                  class="w-full bg-black bg-opacity-50 border border-gray-700 rounded-lg px-4 py-3 text-white focus:border-gold focus:outline-none transition duration-300 h-32"
                  placeholder="Please provide any additional details about your property"
                ></textarea>
              </div>

              <div class="text-center">
                <button
                  type="submit"
                  class="gold-gradient px-10 py-4 rounded-full text-black font-semibold transition duration-300 hover:opacity-90 transform hover:-translate-y-1"
                >
                  Request Free Valuation
                </button>
              </div>
            </form>
            <p class="flex items-center" style="margin-top: 10px">
              <span class="mr-1">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  fill="white"
                  viewBox="0 0 16 16"
                >
                  <path
                    d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z"
                  />
                  <path
                    d="M8.93 6.588l-2.29.287-.082.38.45.083c.294.07.352.176.288.469l-.738 3.468c-.194.897.105 1.319.808 1.319.545 0 1.178-.252 1.465-.598l.088-.416c-.2.176-.492.246-.686.246-.275 0-.375-.193-.304-.533L8.93 6.588zM9 4.5a1 1 0 1 1-2 0 1 1 0 0 1 2 0z"
                  />
                </svg>
              </span>
              <span style="font-size: 9px">Terms And Conditions Apply</span>
            </p>
          </div>
        </div>
      </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="py-20 bg-black">
      <div class="container mx-auto px-5">
        <div class="text-center mb-16" data-aos="fade-up">
          <h2 class="text-3xl md:text-5xl font-bold mb-4 font-playfair">
            <span class="text-white">Contact </span>
            <span class="text-gold">Us</span>
          </h2>
          <p class="text-xl text-gray-300 max-w-3xl mx-auto">
            Get in touch with our team for any inquiries or to arrange a
            consultation
          </p>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
          <div data-aos="fade-right">
            <div class="glass-card p-8 rounded-2xl mb-8">
              <h3 class="text-2xl font-bold mb-6 font-playfair">
                Contact Information
              </h3>
              <ul class="space-y-6">
                <li class="flex items-start">
                  <div class="text-gold text-xl mt-1 mr-4">
                    <i class="fas fa-map-marker-alt"></i>
                  </div>
                  <div>
                    <h4 class="font-semibold mb-1">Office Address</h4>
                    <p class="text-gray-300">Blackburn, Lancashire, UK</p>
                  </div>
                </li>
                <li class="flex items-start">
                  <div class="text-gold text-xl mt-1 mr-4">
                    <i class="fas fa-phone-alt"></i>
                  </div>
                  <div>
                    <h4 class="font-semibold mb-1">Phone Numbers</h4>
                    <p class="text-gray-300">Mobile: 07494 775177</p>
                    <p class="text-gray-300">Telephone: 01254 723664</p>
                  </div>
                </li>
                <li class="flex items-start">
                  <div class="text-gold text-xl mt-1 mr-4">
                    <i class="fas fa-envelope"></i>
                  </div>
                  <div>
                    <h4 class="font-semibold mb-1">Email Address</h4>
                    <p class="text-gray-300"><EMAIL></p>
                  </div>
                </li>
                <li class="flex items-start">
                  <div class="text-gold text-xl mt-1 mr-4">
                    <i class="fas fa-clock"></i>
                  </div>
                  <div>
                    <h4 class="font-semibold mb-1">Opening Hours</h4>
                    <p class="text-gray-300">
                      Monday - Friday: 9:00 AM - 6:00 PM
                    </p>
                    <p class="text-gray-300">Saturday: 10:00 AM - 4:00 PM</p>
                    <p class="text-gray-300">Sunday: Closed</p>
                  </div>
                </li>
              </ul>
            </div>

            <div class="flex space-x-4">
              <a
                href="#"
                class="w-12 h-12 rounded-full flex items-center justify-center gold-gradient text-black transition duration-300 hover:opacity-90"
              >
                <i class="fab fa-facebook-f"></i>
              </a>
              <a
                href="#"
                class="w-12 h-12 rounded-full flex items-center justify-center gold-gradient text-black transition duration-300 hover:opacity-90"
              >
                <i class="fab fa-instagram"></i>
              </a>
              <a
                href="#"
                class="w-12 h-12 rounded-full flex items-center justify-center gold-gradient text-black transition duration-300 hover:opacity-90"
              >
                <i class="fab fa-twitter"></i>
              </a>
              <a
                href="#"
                class="w-12 h-12 rounded-full flex items-center justify-center gold-gradient text-black transition duration-300 hover:opacity-90"
              >
                <i class="fab fa-linkedin-in"></i>
              </a>
            </div>
          </div>

          <div data-aos="fade-left">
            <div class="glass-card p-8 rounded-2xl">
              <h3 class="text-2xl font-bold mb-6 font-playfair">
                Send Us a Message
              </h3>
              <form class="space-y-6" action="https://submit-form.com/b36d4cPGL">
                <div>
                  <label class="block text-gray-300 mb-2">Your Name</label>
                                    <input type="text" class="w-full bg-black bg-opacity-50 border border-gray-700 rounded-lg px-4 py-3 text-white focus:border-gold focus:outline-none transition duration-300" placeholder="Your Name" name="Name" />
                                </div>
                                <div>
                                    <label class="block text-gray-300 mb-2">Email Address</label>
                                    <input type="email" class="w-full bg-black bg-opacity-50 border border-gray-700 rounded-lg px-4 py-3 text-white focus:border-gold focus:outline-none transition duration-300" placeholder="Your Email" name="Email" />
                                </div>
                                <div>
                                    <label class="block text-gray-300 mb-2">Subject</label>
                                    <input type="text" class="w-full bg-black bg-opacity-50 border border-gray-700 rounded-lg px-4 py-3 text-white focus:border-gold focus:outline-none transition duration-300" placeholder="Message Subject" name="Subject" />
                                </div>
                                <div>
                                    <label class="block text-gray-300 mb-2">Message</label>
                                    <textarea class="w-full bg-black bg-opacity-50 border border-gray-700 rounded-lg px-4 py-3 text-white focus:border-gold focus:outline-none transition duration-300 h-32" placeholder="Your Message" name="Message"></textarea>
                                </div>
                                <button type="submit" class="gold-gradient px-8 py-3 rounded-lg text-black font-semibold transition duration-300 hover:opacity-90 w-full">
                  Send Message
                </button>
                        </form>
                        </div>
                        <p class="flex items-center" style="margin-top: 10px">
                            <span class="mr-1">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  fill="white"
                  viewBox="0 0 16 16"
                >
                  <path
                    d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z"
                  />
                  <path
                    d="M8.93 6.588l-2.29.287-.082.38.45.083c.294.07.352.176.288.469l-.738 3.468c-.194.897.105 1.319.808 1.319.545 0 1.178-.252 1.465-.598l.088-.416c-.2.176-.492.246-.686.246-.275 0-.375-.193-.304-.533L8.93 6.588zM9 4.5a1 1 0 1 1-2 0 1 1 0 0 1 2 0z"
                  />
                </svg>
              </span>
                            <span style="font-size: 9px">Terms And Conditions Apply</span>
                        </p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Footer -->
        <footer class="bg-black pt-16 pb-8">
            <div class="container mx-auto px-5">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-12 mb-12">
                    <div>
                        <div class="mb-6">
                            <div class="h-16 w-auto bg-black rounded-lg p-1 inline-block">
                                <img src="images/5-Stars-Lettings-Logo.png" alt="5 Star Lettings & Sales" class="h-full" />
                            </div>
                        </div>
                        <p class="text-gray-400 mb-6">
                            Premium property services in Blackburn with unbeatable value. Specializing in sales and comprehensive property management.
                        </p>
                        <div class="flex space-x-4">
                            <a href="#" class="text-gray-400 hover:text-gold transition duration-300">
                <i class="fab fa-facebook-f"></i>
              </a>
                            <a href="#" class="text-gray-400 hover:text-gold transition duration-300">
                <i class="fab fa-instagram"></i>
              </a>
                            <a href="#" class="text-gray-400 hover:text-gold transition duration-300">
                <i class="fab fa-twitter"></i>
              </a>
                            <a href="#" class="text-gray-400 hover:text-gold transition duration-300">
                <i class="fab fa-linkedin-in"></i>
              </a>
                        </div>
                    </div>

                    <div>
                        <h4 class="text-xl font-bold mb-6 text-white">Quick Links</h4>
                        <ul class="space-y-3">
                            <li>
                                <a href="#home" class="text-gray-400 hover:text-gold transition duration-300">Home</a
                >
              </li>
              <li>
                <a
                  href="#services"
                  class="text-gray-400 hover:text-gold transition duration-300"
                  >Services</a
                >
              </li>
              <li>
                <a
                  href="#properties"
                  class="text-gray-400 hover:text-gold transition duration-300"
                  >Properties</a
                >
              </li>
              <li>
                <a
                  href="#about"
                  class="text-gray-400 hover:text-gold transition duration-300"
                  >About Us</a
                >
              </li>
              <li>
                <a
                  href="#testimonials"
                  class="text-gray-400 hover:text-gold transition duration-300"
                  >Testimonials</a
                >
              </li>
              <li>
                <a
                  href="#contact"
                  class="text-gray-400 hover:text-gold transition duration-300"
                  >Contact</a
                >
              </li>
            </ul>
          </div>

          <div>
            <h4 class="text-xl font-bold mb-6 text-white">
              Contact Information
            </h4>
            <ul class="space-y-3">
              <li class="flex items-start">
                <i class="fas fa-map-marker-alt text-gold mt-1 mr-3"></i>
                <span class="text-gray-400">Blackburn, Lancashire, UK</span>
              </li>
              <li class="flex items-start">
                <i class="fas fa-phone-alt text-gold mt-1 mr-3"></i>
                <span class="text-gray-400">07494 775177</span>
              </li>
              <li class="flex items-start">
                <i class="fas fa-phone text-gold mt-1 mr-3"></i>
                <span class="text-gray-400">01254 723664</span>
              </li>
              <li class="flex items-start">
                <i class="fas fa-envelope text-gold mt-1 mr-3"></i>
                <span class="text-gray-400"><EMAIL></span>
              </li>
            </ul>
          </div>
        </div>

        <div class="border-t border-gray-800 pt-8">
          <div class="flex flex-col md:flex-row justify-between items-center">
            <p class="text-gray-500 mb-4 md:mb-0">
              � 2025 5 Star Lettings & Sales. All rights reserved.
            </p>
            <div class="flex space-x-6">
              <a
                href="#"
                class="text-gray-500 hover:text-gold transition duration-300"
                >Privacy Policy</a
              >
              <a
                href="#"
                class="text-gray-500 hover:text-gold transition duration-300"
                >Terms of Service</a
              >
              <a
                href="#"
                class="text-gray-500 hover:text-gold transition duration-300"
                >Cookie Policy</a
              >
            </div>
          </div>
        </div>
      </div>
    </footer>

    <!-- Back to Top Button -->
    <a
      href="#"
      id="back-to-top"
      class="fixed bottom-8 right-8 w-12 h-12 rounded-full gold-gradient flex items-center justify-center text-black transition duration-300 hover:opacity-90 opacity-0 invisible"
    >
      <i class="fas fa-chevron-up"></i>
    </a>

                                <!-- AOS JS -->
                                <script src="js/aos.js"></script>

                                <!-- Custom JavaScript -->
                                <script>
                                    // Initialize AOS
                                    AOS.init({
                                        once: true,
                                        duration: 800,
                                        offset: 100,
                                    });

                                    // Mobile menu toggle
                                    const mobileMenuButton = document.getElementById("mobile-menu-button");
                                    const mobileMenu = document.getElementById("mobile-menu");

                                    mobileMenuButton.addEventListener("click", () => {
                                        mobileMenu.classList.toggle("hidden");
                                    });

                                    // Navbar scroll effect
                                    const navbar = document.getElementById("navbar");

                                    window.addEventListener("scroll", () => {
                                        if (window.scrollY > 50) {
                                            navbar.classList.add("bg-black", "shadow-lg", "py-2");
                                            navbar.classList.remove("py-3");
                                        } else {
                                            navbar.classList.remove("bg-black", "shadow-lg", "py-2");
                                            navbar.classList.add("py-3");
                                        }
                                    });

                                    // Back to top button
                                    const backToTopButton = document.getElementById("back-to-top");

                                    window.addEventListener("scroll", () => {
                                        if (window.scrollY > 500) {
                                            backToTopButton.classList.remove("opacity-0", "invisible");
                                            backToTopButton.classList.add("opacity-100", "visible");
                                        } else {
                                            backToTopButton.classList.add("opacity-0", "invisible");
                                            backToTopButton.classList.remove("opacity-100", "visible");
                                        }
                                    });

                                    backToTopButton.addEventListener("click", (e) => {
                                        e.preventDefault();
                                        window.scrollTo({
                                            top: 0,
                                            behavior: "smooth"
                                        });
                                    });

                                    // Smooth scrolling for anchor links
                                    document.querySelectorAll('a[href^="#"]').forEach((anchor) => {
                                        anchor.addEventListener("click", function(e) {
                                            e.preventDefault();

                                            const targetId = this.getAttribute("href");
                                            if (targetId === "#") return;

                                            const targetElement = document.querySelector(targetId);
                                            if (targetElement) {
                                                window.scrollTo({
                                                    top: targetElement.offsetTop - 80,
                                                    behavior: "smooth",
                                                });

                                                // Close mobile menu if open
                                                if (!mobileMenu.classList.contains("hidden")) {
                                                    mobileMenu.classList.add("hidden");
                                                }
                                            }
                                        });
                                    });
                                </script>
                                <script>
                                    document.addEventListener('DOMContentLoaded', function() {
                                        // Get both forms
                                        const propertyForm = document.querySelector('form[action="https://submit-form.com/b36d4cPGL"]');
                                        const contactForm = document.querySelectorAll('form[action="https://submit-form.com/b36d4cPGL"]')[1];

                                        // Function to format form data for WhatsApp
                                        function formatDataForWhatsApp(formData) {
                                            let message = '';
                                            for (const [key, value] of formData.entries()) {
                                                if (value && value !== 'Select Property Type') {
                                                    message += `*${key}*: ${value}\n`;
                                                }
                                            }
                                            return encodeURIComponent(message);
                                        }

                                        // Function to detect mobile devices
                                        function isMobileDevice() {
                                            return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
                                        }

                                        // Function to open WhatsApp with better mobile compatibility
                                        function openWhatsApp(phoneNumber, message) {
                                            const whatsappUrl = `https://wa.me/${phoneNumber}?text=${message}`;

                                            // For mobile devices
                                            if (isMobileDevice()) {
                                                // Create an anchor element
                                                const link = document.createElement('a');
                                                link.href = whatsappUrl;
                                                link.setAttribute('target', '_blank');
                                                link.setAttribute('rel', 'noopener noreferrer');

                                                // For iOS specifically
                                                if (/iPhone|iPad|iPod/i.test(navigator.userAgent)) {
                                                    // Try both methods for iOS
                                                    window.location.href = whatsappUrl;
                                                    setTimeout(() => {
                                                        // If the above didn't work, try this as fallback
                                                        link.click();
                                                    }, 300);
                                                } else {
                                                    // For Android and other mobile devices
                                                    link.click();
                                                }
                                            } else {
                                                // For desktop
                                                window.open(whatsappUrl, '_blank');
                                            }
                                        }

                                        // Function to handle form submission
                                        async function handleFormSubmit(event, formType) {
                                            event.preventDefault();

                                            const form = event.target;
                                            const formData = new FormData(form);
                                            const phoneNumber = '447944179480'; // Removed the '+' as it's added in the URL format

                                            try {
                                                // Create a plain object from FormData
                                                const formDataObj = {};
                                                formData.forEach((value, key) => {
                                                    formDataObj[key] = value;
                                                });

                                                // Add form type identifier
                                                formDataObj.formType = formType;

                                                // Show loading indicator or disable button here if needed
                                                const submitButton = form.querySelector('button[type="submit"]');
                                                if (submitButton) {
                                                    submitButton.disabled = true;
                                                    submitButton.innerHTML = 'Sending...';
                                                }

                                                // POST to FormSpark
                                                const response = await fetch('https://submit-form.com/b36d4cPGL', {
                                                    method: 'POST',
                                                    headers: {
                                                        'Content-Type': 'application/json',
                                                        'Accept': 'application/json'
                                                    },
                                                    body: JSON.stringify(formDataObj)
                                                });

                                                if (response.ok) {
                                                    // Format data for WhatsApp
                                                    const whatsappMessage = formatDataForWhatsApp(formData);

                                                    // Reset the form first
                                                    form.reset();

                                                    // Restore button state
                                                    if (submitButton) {
                                                        submitButton.disabled = false;
                                                        submitButton.innerHTML = formType === 'Property Valuation' ? 'Request Free Valuation' : 'Send Message';
                                                    }

                                                    // Display success message
                                                    alert('Form submitted successfully! Redirecting to WhatsApp...');

                                                    // Use a slight delay before opening WhatsApp
                                                    setTimeout(() => {
                                                        openWhatsApp(phoneNumber, whatsappMessage);
                                                    }, 500);

                                                } else {
                                                    console.error('Form submission failed');
                                                    alert('There was an error submitting the form. Please try again.');

                                                    // Restore button state
                                                    if (submitButton) {
                                                        submitButton.disabled = false;
                                                        submitButton.innerHTML = formType === 'Property Valuation' ? 'Request Free Valuation' : 'Send Message';
                                                    }
                                                }
                                            } catch (error) {
                                                console.error('Error:', error);
                                                alert('There was an error submitting the form. Please try again.');

                                                // Restore button state
                                                if (submitButton) {
                                                    submitButton.disabled = false;
                                                    submitButton.innerHTML = formType === 'Property Valuation' ? 'Request Free Valuation' : 'Send Message';
                                                }
                                            }
                                        }

                                        // Add event listeners to both forms
                                        if (propertyForm) {
                                            propertyForm.addEventListener('submit', (e) => handleFormSubmit(e, 'Property Valuation'));
                                        }

                                        if (contactForm) {
                                            contactForm.addEventListener('submit', (e) => handleFormSubmit(e, 'Contact Message'));
                                        }
                                    });
                                </script>
    <script>
        // Your existing JavaScript code...

        // Function to load and display properties on the main page
        function loadAndDisplayProperties() {
            const propertiesContainer = document.getElementById('properties-container');
            const noPropertiesDisplay = document.getElementById('no-properties-display');
            
            if (propertiesContainer && noPropertiesDisplay) {
                // Clear existing properties
                while (propertiesContainer.firstChild) {
                    if (propertiesContainer.firstChild.id !== 'no-properties-display') {
                        propertiesContainer.removeChild(propertiesContainer.firstChild);
                    } else {
                        break;
                    }
                }
                
                // Load properties from localStorage
                const properties = JSON.parse(localStorage.getItem('properties')) || [];
                
                if (properties.length === 0) {
                    noPropertiesDisplay.style.display = 'block';
                } else {
                    noPropertiesDisplay.style.display = 'none';
                    
                    // Display each property
                    properties.forEach(property => {
                        const propertyCard = document.createElement('div');
                        propertyCard.className = 'glass-card rounded-xl overflow-hidden transition-all duration-300 hover:gold-shadow';
                        propertyCard.setAttribute('data-aos', 'fade-up');
                        
                        propertyCard.innerHTML = `
                            <img src="${property.image}" alt="Property" class="w-full h-64 object-cover">
                            <div class="p-6">
                                <div class="flex items-baseline mb-2">
                                    <span class="text-gold text-2xl font-bold">£${Number(property.price).toLocaleString()}</span>
                                </div>
                                <p class="text-gray-300">${property.address}</p>
                                <div class="mt-4">
                                    <a href="#contact" class="gradient-border bg-transparent text-center px-4 py-2 rounded-lg text-white font-medium transition duration-300 hover:text-gold inline-block">
                                        Enquire Now
                                    </a>
                                </div>
                            </div>
                        `;
                        
                        propertiesContainer.appendChild(propertyCard);
                    });
                }
            }
        }

        // Load properties when the page loads
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize AOS and other existing code...
            
            // Load properties
            loadAndDisplayProperties();
            
            // Check for property updates every 5 seconds (in case admin panel is open in another tab)
            setInterval(loadAndDisplayProperties, 5000);
        });
    </script>
</body>

</html>


