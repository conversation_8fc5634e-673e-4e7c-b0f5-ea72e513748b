<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Property Management - 5 Star Lettings & Sales</title>
    <meta name="description" content="Admin panel for managing property listings" />
    <link href="css/tailwind.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="css/all.min.css" />
    <link rel="stylesheet" href="css/aos.css" />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin="" />
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700&family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet" />
    <style>
        :root {
            --gold: #d4af37;
            --gold-light: #f5e7a9;
            --gold-dark: #a67c00;
            --black: #111111;
        }

        body {
            font-family: "Poppins", sans-serif;
        }

        .font-playfair {
            font-family: "Playfair Display", serif;
        }

        .gold-gradient {
            background: linear-gradient(135deg, var(--gold-light), var(--gold), var(--gold-dark));
        }

        .text-gold {
            color: var(--gold);
        }

        .gold-border {
            border-color: var(--gold);
        }

        .gold-shadow {
            box-shadow: 0 4px 20px rgba(212, 175, 55, 0.3);
        }

        .glass-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .property-image-preview {
            width: 100%;
            height: 200px;
            object-fit: cover;
            border-radius: 0.5rem;
        }
    </style>
</head>

<body class="antialiased bg-black text-white">
    <!-- Header -->
    <header class="fixed w-full z-50 transition-all duration-300 bg-black" id="navbar">
        <div class="container mx-auto px-5 py-3">
            <div class="flex justify-between items-center">
                <a href="index.html" class="flex items-center space-x-2">
                    <div class="h-14 w-auto bg-black rounded-lg p-1">
                        <img src="images/Star Letting.png" alt="5 Star Lettings & Sales" class="h-full" />
                    </div>
                </a>

                <nav class="hidden md:flex space-x-8 items-center">
                    <a href="index.html" class="text-white hover:text-gold transition duration-300 font-medium">Back to Website</a>
                </nav>

                <button class="md:hidden text-gold text-2xl" id="mobile-menu-button">
                    <i class="fas fa-bars"></i>
                </button>
            </div>
        </div>

        <!-- Mobile menu -->
        <div class="md:hidden hidden glass-card mt-3 mx-5 rounded-xl p-5 text-center" id="mobile-menu">
            <div class="flex flex-col space-y-4">
                <a href="index.html" class="text-white hover:text-gold transition duration-300 font-medium">Back to Website</a>
            </div>
        </div>
    </header>

    <!-- Admin Panel -->
    <section class="pt-32 pb-20 bg-gradient-to-b from-black to-gray-900">
        <div class="container mx-auto px-5">
            <div class="text-center mb-16" data-aos="fade-up">
                <h2 class="text-3xl md:text-5xl font-bold mb-4 font-playfair">
                    <span class="text-gold">Property</span> Management
                </h2>
                <p class="text-xl text-gray-300 max-w-3xl mx-auto">
                    Add, edit, or remove property listings from your website
                </p>
            </div>

            <!-- Property Form -->
            <div class="glass-card p-8 rounded-2xl mb-12" data-aos="fade-up">
                <h3 class="text-2xl font-bold mb-6 font-playfair">Add New Property</h3>
                <form id="property-form" class="space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-gray-300 mb-2">Property Image</label>
                            <input type="file" id="property-image" accept="image/*" 
                                class="w-full bg-black bg-opacity-50 border border-gray-700 rounded-lg px-4 py-3 text-white focus:border-gold focus:outline-none transition duration-300" />
                            <div class="mt-4">
                                <img id="image-preview" class="property-image-preview hidden" src="#" alt="Property Preview" />
                            </div>
                        </div>
                        <div>
                            <label class="block text-gray-300 mb-2">Property Price (£)</label>
                            <input type="number" id="property-price" 
                                class="w-full bg-black bg-opacity-50 border border-gray-700 rounded-lg px-4 py-3 text-white focus:border-gold focus:outline-none transition duration-300" 
                                placeholder="e.g. 250000" />
                        </div>
                        <div class="md:col-span-2">
                            <label class="block text-gray-300 mb-2">Property Address</label>
                            <textarea id="property-address" 
                                class="w-full bg-black bg-opacity-50 border border-gray-700 rounded-lg px-4 py-3 text-white focus:border-gold focus:outline-none transition duration-300 h-32" 
                                placeholder="Full property address"></textarea>
                        </div>
                    </div>
                    <div class="text-center">
                        <button type="submit" id="add-property-btn"
                            class="gold-gradient px-10 py-4 rounded-full text-black font-semibold transition duration-300 hover:opacity-90 transform hover:-translate-y-1">
                            Add Property
                        </button>
                    </div>
                </form>
            </div>

            <!-- Property Listings -->
            <div class="glass-card p-8 rounded-2xl" data-aos="fade-up" data-aos-delay="100">
                <h3 class="text-2xl font-bold mb-6 font-playfair">Current Properties</h3>
                <div id="property-list" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <!-- Property items will be added here dynamically -->
                    <div class="text-center text-gray-400 col-span-full py-8" id="no-properties-message">
                        No properties have been added yet.
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-black py-8">
        <div class="container mx-auto px-5">
            <div class="border-t border-gray-800 pt-8">
                <div class="flex flex-col md:flex-row justify-between items-center">
                    <p class="text-gray-500 mb-4 md:mb-0">
                        © 2023 5 Star Lettings & Sales. All rights reserved.
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="js/aos.js"></script>
    <script>
        // Initialize AOS
        AOS.init({
            once: true,
            duration: 800,
            offset: 100,
        });

        // Mobile menu toggle
        const mobileMenuButton = document.getElementById('mobile-menu-button');
        const mobileMenu = document.getElementById('mobile-menu');

        mobileMenuButton.addEventListener('click', () => {
            mobileMenu.classList.toggle('hidden');
        });

        // Image preview functionality
        const imageInput = document.getElementById('property-image');
        const imagePreview = document.getElementById('image-preview');

        imageInput.addEventListener('change', function() {
            if (this.files && this.files[0]) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    imagePreview.src = e.target.result;
                    imagePreview.classList.remove('hidden');
                };
                reader.readAsDataURL(this.files[0]);
            }
        });

        // Property management functionality
        const propertyForm = document.getElementById('property-form');
        const propertyList = document.getElementById('property-list');
        const noPropertiesMessage = document.getElementById('no-properties-message');
        
        // Load existing properties from localStorage
        loadProperties();

        // Form submission
        propertyForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const imageInput = document.getElementById('property-image');
            const priceInput = document.getElementById('property-price');
            const addressInput = document.getElementById('property-address');
            
            // Validate inputs
            if (!imageInput.files[0] || !priceInput.value || !addressInput.value) {
                alert('Please fill in all fields');
                return;
            }
            
            // Create new property object
            const reader = new FileReader();
            reader.onload = function(e) {
                const property = {
                    id: Date.now(), // Use timestamp as unique ID
                    image: e.target.result,
                    price: priceInput.value,
                    address: addressInput.value
                };
                
                // Save to localStorage
                saveProperty(property);
                
                // Add to display
                addPropertyToDisplay(property);
                
                // Reset form
                propertyForm.reset();
                imagePreview.classList.add('hidden');
            };
            reader.readAsDataURL(imageInput.files[0]);
        });
        
        // Function to save property to localStorage
        function saveProperty(property) {
            let properties = JSON.parse(localStorage.getItem('properties')) || [];
            properties.push(property);
            localStorage.setItem('properties', JSON.stringify(properties));
        }
        
        // Function to load properties from localStorage
        function loadProperties() {
            const properties = JSON.parse(localStorage.getItem('properties')) || [];
            
            if (properties.length === 0) {
                noPropertiesMessage.classList.remove('hidden');
            } else {
                noPropertiesMessage.classList.add('hidden');
                properties.forEach(property => {
                    addPropertyToDisplay(property);
                });
            }
        }
        
        // Function to add property to display
        function addPropertyToDisplay(property) {
            noPropertiesMessage.classList.add('hidden');
            
            const propertyCard = document.createElement('div');
            propertyCard.className = 'glass-card rounded-xl overflow-hidden transition-all duration-300 hover:gold-shadow';
            propertyCard.dataset.id = property.id;
            
            propertyCard.innerHTML = `
                <img src="${property.image}" alt="Property" class="w-full h-48 object-cover">
                <div class="p-6">
                    <div class="flex items-baseline mb-2">
                        <span class="text-gold text-2xl font-bold">£${Number(property.price).toLocaleString()}</span>
                    </div>
                    <p class="text-gray-300 mb-4">${property.address}</p>
                    <button class="delete-property w-full gradient-border bg-transparent text-center px-4 py-2 rounded-lg text-white font-medium transition duration-300 hover:text-gold">
                        Delete Property
                    </button>
                </div>
            `;
            
            propertyList.appendChild(propertyCard);
            
            // Add delete functionality
            const deleteButton = propertyCard.querySelector('.delete-property');
            deleteButton.addEventListener('click', function() {
                deleteProperty(property.id);
            });
        }
        
        // Function to delete property
        function deleteProperty(id) {
            if (confirm('Are you sure you want to delete this property?')) {
                // Remove from localStorage
                let properties = JSON.parse(localStorage.getItem('properties')) || [];
                properties = properties.filter(property => property.id !== id);
                localStorage.setItem('properties', JSON.stringify(properties));
                
                // Remove from display
                const propertyCard = document.querySelector(`[data-id="${id}"]`);
                if (propertyCard) {
                    propertyCard.remove();
                }
                
                // Show message if no properties left
                if (properties.length === 0) {
                    noPropertiesMessage.classList.remove('hidden');
                }
            }
        }
    </script>
</body>
</html>