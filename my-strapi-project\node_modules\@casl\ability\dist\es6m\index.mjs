import{createFactory as t,$eq as e,$ne as i,$lt as s,$lte as n,$gt as r,$gte as o,$in as c,$nin as u,$all as h,$size as l,$regex as a,$options as f,$elemMatch as d,$exists as y,eq as b,ne as p,lt as w,lte as g,gt as $,gte as A,within as j,nin as M,all as m,size as E,regex as v,elemMatch as x,exists as F,and as _}from"@ucast/mongo2js";function O(t){return Array.isArray(t)?t:[t]}const C=Object.hasOwn||Object.prototype.hasOwnProperty.call.bind(Object.prototype.hasOwnProperty);const R="__caslSubjectType__";function P(t,e){if(e)if(!C(e,R))Object.defineProperty(e,R,{value:t});else if(t!==e[R])throw new Error(`Trying to cast object to subject type ${t} but previously it was casted to ${e[R]}`);return e}const S=t=>{const e=typeof t;return"string"===e||"function"===e};const T=t=>t.modelName||t.name;const B=t=>"string"===typeof t?t:T(t);function q(t){if(C(t,R))return t[R];return T(t.constructor)}function z(t,e,i){let s=O(e);let n=0;while(n<s.length){const e=s[n++];if(C(t,e))s=i(s,t[e])}return s}function D(t,e){if("string"===typeof e&&-1!==t.indexOf(e))return e;for(let i=0;i<e.length;i++)if(-1!==t.indexOf(e[i]))return e[i];return null}const Y=(t,e)=>t.concat(e);function k(t,e){if(e in t)throw new Error(`Cannot use "${e}" as an alias because it's reserved action.`);const i=Object.keys(t);const s=(t,i)=>{const s=D(t,i);if(s)throw new Error(`Detected cycle ${s} -> ${t.join(", ")}`);const n="string"===typeof i&&i===e||-1!==t.indexOf(e)||Array.isArray(i)&&-1!==i.indexOf(e);if(n)throw new Error(`Cannot make an alias to "${e}" because this is reserved action`);return t.concat(i)};for(let e=0;e<i.length;e++)z(t,i[e],s)}function I(t,e){if(!e||false!==e.skipValidate)k(t,e&&e.anyAction||"manage");return e=>z(t,e,Y)}function L(t,e,i){for(let s=i;s<e.length;s++)t.push(e[s])}function U(t,e){if(!t||!t.length)return e||[];if(!e||!e.length)return t||[];let i=0;let s=0;const n=[];while(i<t.length&&s<e.length)if(t[i].priority<e[s].priority){n.push(t[i]);i++}else{n.push(e[s]);s++}L(n,t,i);L(n,e,s);return n}function G(t,e,i){let s=t.get(e);if(!s){s=i();t.set(e,s)}return s}const H=t=>t;function J(t,e){if(Array.isArray(t.fields)&&!t.fields.length)throw new Error("`rawRule.fields` cannot be an empty array. https://bit.ly/390miLa");if(t.fields&&!e.fieldMatcher)throw new Error('You need to pass "fieldMatcher" option in order to restrict access by fields');if(t.conditions&&!e.conditionsMatcher)throw new Error('You need to pass "conditionsMatcher" option in order to restrict access by conditions')}class K{constructor(t,e,i=0){J(t,e);this.action=e.resolveAction(t.action);this.subject=t.subject;this.inverted=!!t.inverted;this.conditions=t.conditions;this.reason=t.reason;this.origin=t;this.fields=t.fields?O(t.fields):void 0;this.priority=i;this.t=e}i(){if(this.conditions&&!this.o)this.o=this.t.conditionsMatcher(this.conditions);return this.o}get ast(){const t=this.i();return t?t.ast:void 0}matchesConditions(t){if(!this.conditions)return true;if(!t||S(t))return!this.inverted;const e=this.i();return e(t)}matchesField(t){if(!this.fields)return true;if(!t)return!this.inverted;if(this.fields&&!this.u)this.u=this.t.fieldMatcher(this.fields);return this.u(t)}}function N(t,e){const i={value:t,prev:e,next:null};if(e)e.next=i;return i}function Q(t){if(t.next)t.next.prev=t.prev;if(t.prev)t.prev.next=t.next;t.next=t.prev=null}const V=t=>({value:t.value,prev:t.prev,next:t.next});const W=()=>({rules:[],merged:false});const X=()=>new Map;class Z{constructor(t=[],e={}){this.h=false;this.l={conditionsMatcher:e.conditionsMatcher,fieldMatcher:e.fieldMatcher,resolveAction:e.resolveAction||H};this.p=e.anyAction||"manage";this.g=e.anySubjectType||"all";this.$=e.detectSubjectType||q;this.A=t;this.j=this.M(t)}get rules(){return this.A}detectSubjectType(t){if(S(t))return t;if(!t)return this.g;return this.$(t)}update(t){const e={rules:t,ability:this,target:this};this.m("update",e);this.A=t;this.j=this.M(t);this.m("updated",e);return this}M(t){const e=new Map;for(let i=t.length-1;i>=0;i--){const s=t.length-i-1;const n=new K(t[i],this.l,s);const r=O(n.action);const o=O(n.subject||this.g);if(!this.h&&n.fields)this.h=true;for(let t=0;t<o.length;t++){const i=G(e,o[t],X);for(let t=0;t<r.length;t++)G(i,r[t],W).rules.push(n)}}return e}possibleRulesFor(t,e=this.g){if(!S(e))throw new Error('"possibleRulesFor" accepts only subject types (i.e., string or class) as the 2nd parameter');const i=G(this.j,e,X);const s=G(i,t,W);if(s.merged)return s.rules;const n=t!==this.p&&i.has(this.p)?i.get(this.p).rules:void 0;let r=U(s.rules,n);if(e!==this.g)r=U(r,this.possibleRulesFor(t,this.g));s.rules=r;s.merged=true;return r}rulesFor(t,e,i){const s=this.possibleRulesFor(t,e);if(i&&"string"!==typeof i)throw new Error("The 3rd, `field` parameter is expected to be a string. See https://stalniy.github.io/casl/en/api/casl-ability#can-of-pure-ability for details");if(!this.h)return s;return s.filter((t=>t.matchesField(i)))}actionsFor(t){if(!S(t))throw new Error('"actionsFor" accepts only subject types (i.e., string or class) as a parameter');const e=new Set;const i=this.j.get(t);if(i)Array.from(i.keys()).forEach((t=>e.add(t)));const s=t!==this.g?this.j.get(this.g):void 0;if(s)Array.from(s.keys()).forEach((t=>e.add(t)));return Array.from(e)}on(t,e){this.v=this.v||new Map;const i=this.v;const s=i.get(t)||null;const n=N(e,s);i.set(t,n);return()=>{const e=i.get(t);if(!n.next&&!n.prev&&e===n)i.delete(t);else if(n===e)i.set(t,n.prev);Q(n)}}m(t,e){if(!this.v)return;let i=this.v.get(t)||null;while(null!==i){const t=i.prev?V(i.prev):null;i.value(e);i=t}}}class PureAbility extends Z{can(t,e,i){const s=this.relevantRuleFor(t,e,i);return!!s&&!s.inverted}relevantRuleFor(t,e,i){const s=this.detectSubjectType(e);const n=this.rulesFor(t,s,i);for(let t=0,i=n.length;t<i;t++)if(n[t].matchesConditions(e))return n[t];return null}cannot(t,e,i){return!this.can(t,e,i)}}const tt={$eq:e,$ne:i,$lt:s,$lte:n,$gt:r,$gte:o,$in:c,$nin:u,$all:h,$size:l,$regex:a,$options:f,$elemMatch:d,$exists:y};const et={eq:b,ne:p,lt:w,lte:g,gt:$,gte:A,in:j,nin:M,all:m,size:E,regex:v,elemMatch:x,exists:F,and:_};const it=(e,i,s)=>t(Object.assign({},tt,e),Object.assign({},et,i),s);const st=t(tt,et);const nt=/[-/\\^$+?.()|[\]{}]/g;const rt=/\.?\*+\.?/g;const ot=/\*+/;const ct=/\./g;function ut(t,e,i){const s="*"===i[0]||"."===t[0]&&"."===t[t.length-1]?"+":"*";const n=-1===t.indexOf("**")?"[^.]":".";const r=t.replace(ct,"\\$&").replace(ot,n+s);return e+t.length===i.length?`(?:${r})?`:r}function ht(t,e,i){if("."===t&&("*"===i[e-1]||"*"===i[e+1]))return t;return`\\${t}`}function lt(t){const e=t.map((t=>t.replace(nt,ht).replace(rt,ut)));const i=e.length>1?`(?:${e.join("|")})`:e[0];return new RegExp(`^${i}$`)}const at=t=>{let e;return i=>{if("undefined"===typeof e)e=t.every((t=>-1===t.indexOf("*")))?null:lt(t);return null===e?-1!==t.indexOf(i):e.test(i)}};class Ability extends PureAbility{constructor(t=[],e={}){super(t,Object.assign({conditionsMatcher:st,fieldMatcher:at},e))}}function createMongoAbility(t=[],e={}){return new PureAbility(t,Object.assign({conditionsMatcher:st,fieldMatcher:at},e))}function isAbilityClass(t){return"function"===typeof t.prototype.possibleRulesFor}class ft{constructor(t){this.F=t}because(t){this.F.reason=t;return this}}class AbilityBuilder{constructor(t){this.rules=[];this._=t;this.can=(t,e,i,s)=>this.O(t,e,i,s,false);this.cannot=(t,e,i,s)=>this.O(t,e,i,s,true);this.build=t=>isAbilityClass(this._)?new this._(this.rules,t):this._(this.rules,t)}O(t,e,i,s,n){const r={action:t};if(n)r.inverted=n;if(e){r.subject=e;if(Array.isArray(i)||"string"===typeof i)r.fields=i;else if("undefined"!==typeof i)r.conditions=i;if("undefined"!==typeof s)r.conditions=s}this.rules.push(r);return new ft(r)}}function defineAbility(t,e){const i=new AbilityBuilder(createMongoAbility);const s=t(i.can,i.cannot);if(s&&"function"===typeof s.then)return s.then((()=>i.build(e)));return i.build(e)}const dt=t=>`Cannot execute "${t.action}" on "${t.subjectType}"`;const yt=function t(e){this.message=e};yt.prototype=Object.create(Error.prototype);class ForbiddenError extends yt{static setDefaultMessage(t){this.C="string"===typeof t?()=>t:t}static from(t){return new this(t)}constructor(t){super("");this.ability=t;if("function"===typeof Error.captureStackTrace){this.name="ForbiddenError";Error.captureStackTrace(this,this.constructor)}}setMessage(t){this.message=t;return this}throwUnlessCan(t,e,i){const s=this.unlessCan(t,e,i);if(s)throw s}unlessCan(t,e,i){const s=this.ability.relevantRuleFor(t,e,i);if(s&&!s.inverted)return;this.action=t;this.subject=e;this.subjectType=B(this.ability.detectSubjectType(e));this.field=i;const n=s?s.reason:"";this.message=this.message||n||this.constructor.C(this);return this}}ForbiddenError.C=dt;var bt=Object.freeze({__proto__:null});export{Ability,AbilityBuilder,ForbiddenError,PureAbility,it as buildMongoQueryMatcher,I as createAliasResolver,createMongoAbility,defineAbility,q as detectSubjectType,at as fieldPatternMatcher,dt as getDefaultErrorMessage,bt as hkt,st as mongoQueryMatcher,P as subject,O as wrapArray};
//# sourceMappingURL=index.mjs.map
