{"version": 3, "file": "PrefixProxyTerminalProvider.js", "sourceRoot": "", "sources": ["../src/PrefixProxyTerminalProvider.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;;AAE3D,oEAAoD;AA6CpD;;;;;GAKG;AACH,MAAa,2BAA2B;IAMtC,YAAmB,OAA4C;QAC7D,MAAM,EAAE,gBAAgB,EAAE,GAAG,OAAO,CAAC;QAErC,IAAI,CAAC,uBAAuB,GAAG,gBAAgB,CAAC;QAEhD,IAAK,OAAqD,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;YAChF,MAAM,EAAE,MAAM,EAAE,GAAG,OAAoD,CAAC;YACxE,IAAI,CAAC,UAAU,GAAG,GAAG,EAAE,CAAC,MAAM,CAAC;QACjC,CAAC;aAAM,CAAC;YACN,MAAM,EAAE,SAAS,EAAE,GAAG,OAAqD,CAAC;YAC5E,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;QAC9B,CAAC;QAED,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QAEzB,gEAAgE;QAChE,IAAI,CAAC,aAAa,GAAG,IAAI,MAAM,CAAC,GAAG,wBAAI,CAAC,YAAY,CAAC,gBAAgB,CAAC,YAAY,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;IAClG,CAAC;IAED,gBAAgB;IAChB,IAAW,aAAa;QACtB,OAAO,IAAI,CAAC,uBAAuB,CAAC,aAAa,CAAC;IACpD,CAAC;IAED,gBAAgB;IAChB,IAAW,YAAY;QACrB,OAAO,IAAI,CAAC,uBAAuB,CAAC,YAAY,CAAC;IACnD,CAAC;IAED,gBAAgB;IACT,KAAK,CAAC,IAAY,EAAE,QAAkC;QAC3D,4EAA4E;QAC5E,IAAI,YAAY,GAAW,CAAC,CAAC;QAC7B,kDAAkD;QAClD,IAAI,YAAoC,CAAC;QAEzC,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC;YACtD,sEAAsE;YACtE,MAAM,YAAY,GAAW,YAAY,CAAC,KAAK,CAAC;YAChD,MAAM,QAAQ,GAAW,YAAY,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;YAC/D,MAAM,MAAM,GAAW,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;YAClE,MAAM,WAAW,GAAW,GAAG,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,YAAY,EAAE,QAAQ,CAAC,EAAE,CAAC;YACjF,IAAI,CAAC,uBAAuB,CAAC,KAAK,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;YAC1D,8EAA8E;YAC9E,YAAY,GAAG,QAAQ,CAAC;YACxB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QAC3B,CAAC;QAED,sGAAsG;QACtG,MAAM,aAAa,GAAW,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;QAC3D,IAAI,aAAa,CAAC,MAAM,EAAE,CAAC;YACzB,MAAM,MAAM,GAAW,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;YAClE,IAAI,CAAC,uBAAuB,CAAC,KAAK,CAAC,GAAG,MAAM,GAAG,aAAa,EAAE,EAAE,QAAQ,CAAC,CAAC;YAC1E,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;QAC5B,CAAC;IACH,CAAC;CACF;AA9DD,kEA8DC", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\n// See LICENSE in the project root for license information.\n\nimport { Text } from '@rushstack/node-core-library';\nimport type { ITerminalProvider, TerminalProviderSeverity } from './ITerminalProvider';\n\n/**\n * @beta\n */\nexport interface IPrefixProxyTerminalProviderOptionsBase {\n  /**\n   * The {@link ITerminalProvider} that will be wrapped.\n   */\n  terminalProvider: ITerminalProvider;\n}\n\n/**\n * Options for {@link PrefixProxyTerminalProvider}, with a static prefix.\n *\n * @beta\n */\nexport interface IStaticPrefixProxyTerminalProviderOptions extends IPrefixProxyTerminalProviderOptionsBase {\n  /**\n   * The prefix that should be added to each line of output.\n   */\n  prefix: string;\n}\n\n/**\n * Options for {@link PrefixProxyTerminalProvider}.\n *\n * @beta\n */\nexport interface IDynamicPrefixProxyTerminalProviderOptions extends IPrefixProxyTerminalProviderOptionsBase {\n  /**\n   * A function that returns the prefix that should be added to each line of output. This is useful\n   * for prefixing each line with a timestamp.\n   */\n  getPrefix: () => string;\n}\n\n/**\n * @beta\n */\nexport type IPrefixProxyTerminalProviderOptions =\n  | IStaticPrefixProxyTerminalProviderOptions\n  | IDynamicPrefixProxyTerminalProviderOptions;\n\n/**\n * Wraps an existing {@link ITerminalProvider} that prefixes each line of output with a specified\n * prefix string.\n *\n * @beta\n */\nexport class PrefixProxyTerminalProvider implements ITerminalProvider {\n  private readonly _parentTerminalProvider: ITerminalProvider;\n  private readonly _getPrefix: () => string;\n  private readonly _newlineRegex: RegExp;\n  private _isOnNewline: boolean;\n\n  public constructor(options: IPrefixProxyTerminalProviderOptions) {\n    const { terminalProvider } = options;\n\n    this._parentTerminalProvider = terminalProvider;\n\n    if ((options as IStaticPrefixProxyTerminalProviderOptions).prefix !== undefined) {\n      const { prefix } = options as IStaticPrefixProxyTerminalProviderOptions;\n      this._getPrefix = () => prefix;\n    } else {\n      const { getPrefix } = options as IDynamicPrefixProxyTerminalProviderOptions;\n      this._getPrefix = getPrefix;\n    }\n\n    this._isOnNewline = true;\n\n    // eslint-disable-next-line @rushstack/security/no-unsafe-regexp\n    this._newlineRegex = new RegExp(`${Text.escapeRegExp(terminalProvider.eolCharacter)}|\\\\n`, 'g');\n  }\n\n  /** @override */\n  public get supportsColor(): boolean {\n    return this._parentTerminalProvider.supportsColor;\n  }\n\n  /** @override */\n  public get eolCharacter(): string {\n    return this._parentTerminalProvider.eolCharacter;\n  }\n\n  /** @override */\n  public write(data: string, severity: TerminalProviderSeverity): void {\n    // We need to track newlines to ensure that the prefix is added to each line\n    let currentIndex: number = 0;\n    // eslint-disable-next-line @rushstack/no-new-null\n    let newlineMatch: RegExpExecArray | null;\n\n    while ((newlineMatch = this._newlineRegex.exec(data))) {\n      // Extract the line, add the prefix, and write it out with the newline\n      const newlineIndex: number = newlineMatch.index;\n      const newIndex: number = newlineIndex + newlineMatch[0].length;\n      const prefix: string = this._isOnNewline ? this._getPrefix() : '';\n      const dataToWrite: string = `${prefix}${data.substring(currentIndex, newIndex)}`;\n      this._parentTerminalProvider.write(dataToWrite, severity);\n      // Update the currentIndex to start the search from the char after the newline\n      currentIndex = newIndex;\n      this._isOnNewline = true;\n    }\n\n    // The remaining data is not postfixed by a newline, so write out the data and set _isNewline to false\n    const remainingData: string = data.substring(currentIndex);\n    if (remainingData.length) {\n      const prefix: string = this._isOnNewline ? this._getPrefix() : '';\n      this._parentTerminalProvider.write(`${prefix}${remainingData}`, severity);\n      this._isOnNewline = false;\n    }\n  }\n}\n"]}