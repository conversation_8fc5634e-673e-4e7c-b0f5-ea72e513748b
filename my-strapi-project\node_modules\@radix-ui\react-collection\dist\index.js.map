{"mappings": ";;;;;;;;;;;;;ACAA;;;;AAaA,6FAAA;AACA,oEAAA;AACA,8FAAA;AACA,yDAAA;AAEA,SAASA,yCAAT,CAA0EK,IAA1E,EAAwF;IACtF;;oGAEF,CAEE,MAAMC,aAAa,GAAGD,IAAI,GAAG,oBAA7B,AAAA;IACA,MAAM,CAACE,uBAAD,EAA0BC,qBAA1B,CAAA,GAAmDN,6CAAkB,CAACI,aAAD,CAA3E,AAAA;IAOA,MAAM,CAACG,sBAAD,EAAyBC,oBAAzB,CAAA,GAAiDH,uBAAuB,CAC5ED,aAD4E,EAE5E;QAAEK,aAAa,EAAE;YAAEC,OAAO,EAAE,IAATA;SAAnB;QAAoCC,OAAO,EAAE,IAAIC,GAAJ,EAATD;KAFwC,CAA9E,AAEE;IAGF,MAAME,kBAAwE,GAAIC,CAAAA,KAAD,GAAW;QAC1F,MAAM,EArCV,OAqCYC,KAAF,CAAA,EArCV,UAqCmBC,QAAAA,CAAAA,EAAT,GAAsBF,KAA5B,AAAM;QACN,MAAMG,GAAG,GAAGlB,sCAAK,CAACmB,MAAN,CAAgC,IAAhC,CAAZ,AAAA;QACA,MAAMP,OAAO,GAAGZ,sCAAK,CAACmB,MAAN,CAAsC,IAAIN,GAAJ,EAAtC,CAAA,CAAiDF,OAAjE,AAAA;QACA,OAAA,aACE,CAAA,sCAAA,CAAA,aAAA,CAAC,sBAAD,EADF;YAC0B,KAAK,EAAEK,KAA/B;YAAsC,OAAO,EAAEJ,OAA/C;YAAwD,aAAa,EAAEM,GAAf;SAAxD,EACGD,QADH,CADF,CACE;KALJ,AASC;IAED,aAAA,CAAA,MAAA,CAAA,MAAA,CAAA,kBAAA,EAAA;QAAA,WAAA,EAAA,aAAA;KAAA,CAAA,CAAA;IAEA;;oGAEF,CAEE,MAAMG,oBAAoB,GAAGhB,IAAI,GAAG,gBAApC,AAAA;IAEA,MAAMiB,cAAc,GAAA,aAAGrB,CAAAA,sCAAK,CAACsB,UAAN,CACrB,CAACP,KAAD,EAAQQ,YAAR,GAAyB;QACvB,MAAM,EAzDZ,OAyDcP,KAAF,CAAA,EAzDZ,UAyDqBC,QAAAA,CAAAA,EAAT,GAAsBF,KAA5B,AAAM;QACN,MAAMS,OAAO,GAAGf,oBAAoB,CAACW,oBAAD,EAAuBJ,KAAvB,CAApC,AAAA;QACA,MAAMS,YAAY,GAAGvB,8CAAe,CAACqB,YAAD,EAAeC,OAAO,CAACd,aAAvB,CAApC,AAAA;QACA,OAAA,aAAO,CAAA,sCAAA,CAAA,aAAA,CAAC,4BAAD,EAAP;YAAa,GAAG,EAAEe,YAAL;SAAN,EAA0BR,QAA1B,CAAP,CAAO;KALY,CAAvB,AAMG;IAGH,aAAA,CAAA,MAAA,CAAA,MAAA,CAAA,cAAA,EAAA;QAAA,WAAA,EAAA,oBAAA;KAAA,CAAA,CAAA;IAEA;;oGAEF,CAEE,MAAMS,cAAc,GAAGtB,IAAI,GAAG,oBAA9B,AAAA;IACA,MAAMuB,cAAc,GAAG,4BAAvB,AAAA;IAOA,MAAMC,kBAAkB,GAAA,aAAG5B,CAAAA,sCAAK,CAACsB,UAAN,CACzB,CAACP,KAAD,EAAQQ,YAAR,GAAyB;QACvB,MAAM,EAhFZ,OAgFcP,KAAF,CAAA,EAhFZ,UAgFqBC,QAAT,CAAA,EAAmB,GAAGY,QAAH,EAAnB,GAAmCd,KAAzC,AAAM;QACN,MAAMG,GAAG,GAAGlB,sCAAK,CAACmB,MAAN,CAA0B,IAA1B,CAAZ,AAAA;QACA,MAAMM,YAAY,GAAGvB,8CAAe,CAACqB,YAAD,EAAeL,GAAf,CAApC,AAAA;QACA,MAAMM,OAAO,GAAGf,oBAAoB,CAACiB,cAAD,EAAiBV,KAAjB,CAApC,AAAA;QAEAhB,sCAAK,CAAC8B,SAAN,CAAgB,IAAM;YACpBN,OAAO,CAACZ,OAAR,CAAgBmB,GAAhB,CAAoBb,GAApB,EAAyB;gBAtFjC,KAsFmCA,GAAF;gBAAO,GAAIW,QAAJ;aAAhC,CAAyB,CAAA;YACzB,OAAO,IAAM,KAAKL,OAAO,CAACZ,OAAR,CAAgBoB,MAAhB,CAAuBd,GAAvB,CAAlB;YAAA,CAAA;SAFF,CAGC,CAAA;QAED,OAAA,aACE,CAAA,sCAAA,CAAA,aAAA,CAAC,4BAAD,EADF;YACc,CAACS,cAAD,CAAA,EAAkB,EAA9B;YAAoC,GAAG,EAAEF,YAAL;SAApC,EACGR,QADH,CADF,CACE;KAbqB,CAA3B,AAiBG;IAGH,aAAA,CAAA,MAAA,CAAA,MAAA,CAAA,kBAAA,EAAA;QAAA,WAAA,EAAA,cAAA;KAAA,CAAA,CAAA;IAEA;;oGAEF,CAEE,SAASgB,aAAT,CAAuBjB,KAAvB,EAAmC;QACjC,MAAMQ,OAAO,GAAGf,oBAAoB,CAACL,IAAI,GAAG,oBAAR,EAA8BY,KAA9B,CAApC,AAAA;QAEA,MAAMkB,QAAQ,GAAGlC,sCAAK,CAACmC,WAAN,CAAkB,IAAM;YACvC,MAAMC,cAAc,GAAGZ,OAAO,CAACd,aAAR,CAAsBC,OAA7C,AAAA;YACA,IAAI,CAACyB,cAAL,EAAqB,OAAO,EAAP,CAArB;YACA,MAAMC,YAAY,GAAGC,KAAK,CAACC,IAAN,CAAWH,cAAc,CAACI,gBAAf,CAAiC,CAAA,CAAA,EAAGb,cAAe,CAAA,CAAA,CAAnD,CAAX,CAArB,AAAA;YACA,MAAMc,KAAK,GAAGH,KAAK,CAACC,IAAN,CAAWf,OAAO,CAACZ,OAAR,CAAgB8B,MAAhB,EAAX,CAAd,AAAA;YACA,MAAMC,YAAY,GAAGF,KAAK,CAACG,IAAN,CACnB,CAACC,CAAD,EAAIC,CAAJ,GAAUT,YAAY,CAACU,OAAb,CAAqBF,CAAC,CAAC3B,GAAF,CAAMP,OAA3B,CAAA,GAAuC0B,YAAY,CAACU,OAAb,CAAqBD,CAAC,CAAC5B,GAAF,CAAMP,OAA3B,CAD9B;YAAA,CAArB,AAAA;YAGA,OAAOgC,YAAP,CAAA;SARe,EASd;YAACnB,OAAO,CAACd,aAAT;YAAwBc,OAAO,CAACZ,OAAhC;SATc,CAAjB,AASC;QAED,OAAOsB,QAAP,CAAA;KACD;IAED,OAAO;QACL;YAAEc,QAAQ,EAAElC,kBAAZ;YAAgCX,IAAI,EAAEkB,cAAtC;YAAsD4B,QAAQ,EAAErB,kBAAVqB;SADjD;QAELhB,aAFK;QAGL1B,qBAHK;KAAP,CACE;CAIH;;AD9HD", "sources": ["packages/react/collection/src/index.ts", "packages/react/collection/src/Collection.tsx"], "sourcesContent": ["export { createCollection } from './Collection';\nexport type { CollectionProps } from './Collection';\n", "import React from 'react';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { Slot } from '@radix-ui/react-slot';\n\nimport type * as Radix from '@radix-ui/react-primitive';\n\ntype SlotProps = Radix.ComponentPropsWithoutRef<typeof Slot>;\ntype CollectionElement = HTMLElement;\ninterface CollectionProps extends SlotProps {\n  scope: any;\n}\n\n// We have resorted to returning slots directly rather than exposing primitives that can then\n// be slotted like `<CollectionItem as={Slot}>…</CollectionItem>`.\n// This is because we encountered issues with generic types that cannot be statically analysed\n// due to creating them dynamically via createCollection.\n\nfunction createCollection<ItemElement extends HTMLElement, ItemData = {}>(name: string) {\n  /* -----------------------------------------------------------------------------------------------\n   * CollectionProvider\n   * ---------------------------------------------------------------------------------------------*/\n\n  const PROVIDER_NAME = name + 'CollectionProvider';\n  const [createCollectionContext, createCollectionScope] = createContextScope(PROVIDER_NAME);\n\n  type ContextValue = {\n    collectionRef: React.RefObject<CollectionElement>;\n    itemMap: Map<React.RefObject<ItemElement>, { ref: React.RefObject<ItemElement> } & ItemData>;\n  };\n\n  const [CollectionProviderImpl, useCollectionContext] = createCollectionContext<ContextValue>(\n    PROVIDER_NAME,\n    { collectionRef: { current: null }, itemMap: new Map() }\n  );\n\n  const CollectionProvider: React.FC<{ children?: React.ReactNode; scope: any }> = (props) => {\n    const { scope, children } = props;\n    const ref = React.useRef<CollectionElement>(null);\n    const itemMap = React.useRef<ContextValue['itemMap']>(new Map()).current;\n    return (\n      <CollectionProviderImpl scope={scope} itemMap={itemMap} collectionRef={ref}>\n        {children}\n      </CollectionProviderImpl>\n    );\n  };\n\n  CollectionProvider.displayName = PROVIDER_NAME;\n\n  /* -----------------------------------------------------------------------------------------------\n   * CollectionSlot\n   * ---------------------------------------------------------------------------------------------*/\n\n  const COLLECTION_SLOT_NAME = name + 'CollectionSlot';\n\n  const CollectionSlot = React.forwardRef<CollectionElement, CollectionProps>(\n    (props, forwardedRef) => {\n      const { scope, children } = props;\n      const context = useCollectionContext(COLLECTION_SLOT_NAME, scope);\n      const composedRefs = useComposedRefs(forwardedRef, context.collectionRef);\n      return <Slot ref={composedRefs}>{children}</Slot>;\n    }\n  );\n\n  CollectionSlot.displayName = COLLECTION_SLOT_NAME;\n\n  /* -----------------------------------------------------------------------------------------------\n   * CollectionItem\n   * ---------------------------------------------------------------------------------------------*/\n\n  const ITEM_SLOT_NAME = name + 'CollectionItemSlot';\n  const ITEM_DATA_ATTR = 'data-radix-collection-item';\n\n  type CollectionItemSlotProps = ItemData & {\n    children: React.ReactNode;\n    scope: any;\n  };\n\n  const CollectionItemSlot = React.forwardRef<ItemElement, CollectionItemSlotProps>(\n    (props, forwardedRef) => {\n      const { scope, children, ...itemData } = props;\n      const ref = React.useRef<ItemElement>(null);\n      const composedRefs = useComposedRefs(forwardedRef, ref);\n      const context = useCollectionContext(ITEM_SLOT_NAME, scope);\n\n      React.useEffect(() => {\n        context.itemMap.set(ref, { ref, ...(itemData as unknown as ItemData) });\n        return () => void context.itemMap.delete(ref);\n      });\n\n      return (\n        <Slot {...{ [ITEM_DATA_ATTR]: '' }} ref={composedRefs}>\n          {children}\n        </Slot>\n      );\n    }\n  );\n\n  CollectionItemSlot.displayName = ITEM_SLOT_NAME;\n\n  /* -----------------------------------------------------------------------------------------------\n   * useCollection\n   * ---------------------------------------------------------------------------------------------*/\n\n  function useCollection(scope: any) {\n    const context = useCollectionContext(name + 'CollectionConsumer', scope);\n\n    const getItems = React.useCallback(() => {\n      const collectionNode = context.collectionRef.current;\n      if (!collectionNode) return [];\n      const orderedNodes = Array.from(collectionNode.querySelectorAll(`[${ITEM_DATA_ATTR}]`));\n      const items = Array.from(context.itemMap.values());\n      const orderedItems = items.sort(\n        (a, b) => orderedNodes.indexOf(a.ref.current!) - orderedNodes.indexOf(b.ref.current!)\n      );\n      return orderedItems;\n    }, [context.collectionRef, context.itemMap]);\n\n    return getItems;\n  }\n\n  return [\n    { Provider: CollectionProvider, Slot: CollectionSlot, ItemSlot: CollectionItemSlot },\n    useCollection,\n    createCollectionScope,\n  ] as const;\n}\n\nexport { createCollection };\nexport type { CollectionProps };\n"], "names": ["createCollection", "React", "createContextScope", "useComposedRefs", "Slot", "name", "PROVIDER_NAME", "createCollectionContext", "createCollectionScope", "CollectionProviderImpl", "useCollectionContext", "collectionRef", "current", "itemMap", "Map", "CollectionProvider", "props", "scope", "children", "ref", "useRef", "COLLECTION_SLOT_NAME", "CollectionSlot", "forwardRef", "forwardedRef", "context", "composedRefs", "ITEM_SLOT_NAME", "ITEM_DATA_ATTR", "CollectionItemSlot", "itemData", "useEffect", "set", "delete", "useCollection", "getItems", "useCallback", "collectionNode", "orderedNodes", "Array", "from", "querySelectorAll", "items", "values", "orderedItems", "sort", "a", "b", "indexOf", "Provider", "ItemSlot"], "version": 3, "file": "index.js.map"}