{"version": 3, "file": "FileSystem.js", "sourceRoot": "", "sources": ["../src/FileSystem.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE3D,iDAAmC;AACnC,uCAAyB;AACzB,wDAA0C;AAC1C,8CAAgC;AAEhC,iCAA0D;AAC1D,mDAAgD;AA8IhD;;;;;;;;;;;;GAYG;AACH,IAAY,qBAwBX;AAxBD,WAAY,qBAAqB;IAC/B;;;;;;;;;;OAUG;IACH,gDAAuB,CAAA;IAEvB;;;OAGG;IACH,wCAAe,CAAA;IAEf;;OAEG;IACH,0CAAiB,CAAA;AACnB,CAAC,EAxBW,qBAAqB,qCAArB,qBAAqB,QAwBhC;AAmID,MAAM,oBAAoB,GAAoC;IAC5D,SAAS,EAAE,IAAI;IACf,kBAAkB,EAAE,KAAK;CAC1B,CAAC;AAEF,MAAM,2BAA2B,GAA0C;IACzE,aAAa,EAAE,KAAK;CACrB,CAAC;AAEF,MAAM,0BAA0B,GAAyC;IACvE,kBAAkB,EAAE,KAAK;IACzB,kBAAkB,EAAE,SAAS;IAC7B,QAAQ,EAAE,eAAQ,CAAC,IAAI;CACxB,CAAC;AAEF,MAAM,8BAA8B,qBAC/B,0BAA0B,CAC9B,CAAC;AAEF,MAAM,yBAAyB,GAAwC;IACrE,QAAQ,EAAE,eAAQ,CAAC,IAAI;IACvB,kBAAkB,EAAE,SAAS;CAC9B,CAAC;AAEF,MAAM,yBAAyB,GAAwC;IACrE,qBAAqB,EAAE,qBAAqB,CAAC,SAAS;CACvD,CAAC;AAEF,MAAM,0BAA0B,GAAyC;IACvE,qBAAqB,EAAE,qBAAqB,CAAC,SAAS;CACvD,CAAC;AAEF,MAAM,2BAA2B,GAA0C;IACzE,gBAAgB,EAAE,KAAK;CACxB,CAAC;AAEF;;;;;;;;;;;;;;;GAeG;AACH,MAAa,UAAU;IACrB,kBAAkB;IAClB,oBAAoB;IACpB,kBAAkB;IAElB;;;;;;;;;;OAUG;IACI,MAAM,CAAC,MAAM,CAAC,IAAY;QAC/B,OAAO,UAAU,CAAC,cAAc,CAAC,GAAG,EAAE;YACpC,OAAO,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,IAAY;QAC1C,OAAO,MAAM,UAAU,CAAC,mBAAmB,CAAC,GAAG,EAAE;YAC/C,OAAO,IAAI,OAAO,CAAU,CAAC,OAAkC,EAAE,EAAE;gBACjE,GAAG,CAAC,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;YAC5B,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;OAKG;IACI,MAAM,CAAC,aAAa,CAAC,IAAY;QACtC,OAAO,UAAU,CAAC,cAAc,CAAC,GAAG,EAAE;YACpC,OAAO,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,KAAK,CAAC,kBAAkB,CAAC,IAAY;QACjD,OAAO,MAAM,UAAU,CAAC,mBAAmB,CAAC,GAAG,EAAE;YAC/C,OAAO,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACxB,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;;OAMG;IACI,MAAM,CAAC,WAAW,CAAC,IAAY,EAAE,KAAsC;QAC5E,OAAO,UAAU,CAAC,cAAc,CAAC,GAAG,EAAE;YACpC,GAAG,CAAC,UAAU,CAAC,IAAI,EAAE,KAAK,CAAC,YAAY,EAAE,KAAK,CAAC,YAAY,CAAC,CAAC;QAC/D,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,KAAK,CAAC,gBAAgB,CAAC,IAAY,EAAE,KAAsC;QACvF,MAAM,UAAU,CAAC,mBAAmB,CAAC,GAAG,EAAE;YACxC,2EAA2E;YAC3E,iFAAiF;YACjF,OAAO,GAAG,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,YAAsB,EAAE,KAAK,CAAC,YAAsB,CAAC,CAAC;QACtF,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;OAKG;IACI,MAAM,CAAC,mBAAmB,CAAC,IAAY,EAAE,QAAuB;QACrE,UAAU,CAAC,cAAc,CAAC,GAAG,EAAE;YAC7B,EAAE,CAAC,SAAS,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;QAC/B,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,KAAK,CAAC,wBAAwB,CAAC,IAAY,EAAE,IAAmB;QAC5E,MAAM,UAAU,CAAC,mBAAmB,CAAC,GAAG,EAAE;YACxC,OAAO,GAAG,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QAC/B,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;OASG;IACI,MAAM,CAAC,gBAAgB,CAAC,IAAY;QACzC,OAAO,UAAU,CAAC,cAAc,CAAC,GAAG,EAAE;YACpC,OAAO,UAAU,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC;QAC7C,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,KAAK,CAAC,qBAAqB,CAAC,IAAY;QACpD,OAAO,MAAM,UAAU,CAAC,mBAAmB,CAAC,KAAK,IAAI,EAAE;YACrD,OAAO,CAAC,MAAM,UAAU,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC;QAC1D,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;;OAMG;IACI,MAAM,CAAC,mBAAmB,CAAC,QAAuB;QACvD,IAAI,MAAM,GAAW,GAAG,CAAC,CAAC,8EAA8E;QAExG,MAAM,IAAI,QAAQ,GAAG,6BAAa,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;QACxD,MAAM,IAAI,QAAQ,GAAG,6BAAa,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;QACzD,MAAM,IAAI,QAAQ,GAAG,6BAAa,CAAC,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;QAE3D,MAAM,IAAI,QAAQ,GAAG,6BAAa,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;QACzD,MAAM,IAAI,QAAQ,GAAG,6BAAa,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;QAC1D,MAAM,IAAI,QAAQ,GAAG,6BAAa,CAAC,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;QAE5D,MAAM,IAAI,QAAQ,GAAG,6BAAa,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;QAC1D,MAAM,IAAI,QAAQ,GAAG,6BAAa,CAAC,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;QAC3D,MAAM,IAAI,QAAQ,GAAG,6BAAa,CAAC,aAAa,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;QAE7D,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;;OAGG;IACI,MAAM,CAAC,IAAI,CAAC,OAA+B;QAChD,UAAU,CAAC,cAAc,CAAC,GAAG,EAAE;YAC7B,OAAO,mCACF,oBAAoB,GACpB,OAAO,CACX,CAAC;YAEF,IAAI,CAAC;gBACH,GAAG,CAAC,QAAQ,CAAC,OAAO,CAAC,UAAU,EAAE,OAAO,CAAC,eAAe,EAAE,EAAE,SAAS,EAAE,OAAO,CAAC,SAAS,EAAE,CAAC,CAAC;YAC9F,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAI,OAAO,CAAC,kBAAkB,EAAE,CAAC;oBAC/B,IAAI,CAAC,UAAU,CAAC,eAAe,CAAC,KAAc,CAAC,EAAE,CAAC;wBAChD,MAAM,KAAK,CAAC;oBACd,CAAC;oBAED,MAAM,UAAU,GAAW,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;oBACvE,UAAU,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;oBACpC,GAAG,CAAC,QAAQ,CAAC,OAAO,CAAC,UAAU,EAAE,OAAO,CAAC,eAAe,EAAE,EAAE,SAAS,EAAE,OAAO,CAAC,SAAS,EAAE,CAAC,CAAC;gBAC9F,CAAC;qBAAM,CAAC;oBACN,MAAM,KAAK,CAAC;gBACd,CAAC;YACH,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,OAA+B;QAC3D,MAAM,UAAU,CAAC,mBAAmB,CAAC,KAAK,IAAI,EAAE;YAC9C,OAAO,mCACF,oBAAoB,GACpB,OAAO,CACX,CAAC;YAEF,IAAI,CAAC;gBACH,MAAM,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,OAAO,CAAC,eAAe,EAAE,EAAE,SAAS,EAAE,OAAO,CAAC,SAAS,EAAE,CAAC,CAAC;YAChG,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAI,OAAO,CAAC,kBAAkB,EAAE,CAAC;oBAC/B,IAAI,CAAC,UAAU,CAAC,eAAe,CAAC,KAAc,CAAC,EAAE,CAAC;wBAChD,MAAM,KAAK,CAAC;oBACd,CAAC;oBAED,MAAM,UAAU,GAAW,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;oBACvE,MAAM,UAAU,CAAC,iBAAiB,CAAC,UAAU,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC;oBACnE,MAAM,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,OAAO,CAAC,eAAe,EAAE,EAAE,SAAS,EAAE,OAAO,CAAC,SAAS,EAAE,CAAC,CAAC;gBAChG,CAAC;qBAAM,CAAC;oBACN,MAAM,KAAK,CAAC;gBACd,CAAC;YACH,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED,kBAAkB;IAClB,oBAAoB;IACpB,kBAAkB;IAElB;;;;;;OAMG;IACI,MAAM,CAAC,YAAY,CAAC,UAAkB;QAC3C,UAAU,CAAC,cAAc,CAAC,GAAG,EAAE;YAC7B,GAAG,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;QAChC,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,KAAK,CAAC,iBAAiB,CAAC,UAAkB;QACtD,MAAM,UAAU,CAAC,mBAAmB,CAAC,GAAG,EAAE;YACxC,OAAO,GAAG,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;QACnC,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;OAKG;IACI,MAAM,CAAC,mBAAmB,CAAC,UAAkB,EAAE,OAAsC;QAC1F,OAAO,UAAU,CAAC,cAAc,CAAC,GAAG,EAAE;YACpC,OAAO,mCACF,2BAA2B,GAC3B,OAAO,CACX,CAAC;YAEF,MAAM,SAAS,GAAa,GAAG,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;YACxD,IAAI,OAAO,CAAC,aAAa,EAAE,CAAC;gBAC1B,OAAO,SAAS,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC,UAAU,CAAC,OAAO,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC,CAAC;YAC/E,CAAC;iBAAM,CAAC;gBACN,OAAO,SAAS,CAAC;YACnB,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,KAAK,CAAC,wBAAwB,CAC1C,UAAkB,EAClB,OAAsC;QAEtC,OAAO,MAAM,UAAU,CAAC,mBAAmB,CAAC,KAAK,IAAI,EAAE;YACrD,OAAO,mCACF,2BAA2B,GAC3B,OAAO,CACX,CAAC;YAEF,MAAM,SAAS,GAAa,MAAM,GAAG,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;YAC1D,IAAI,OAAO,CAAC,aAAa,EAAE,CAAC;gBAC1B,OAAO,SAAS,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC,UAAU,CAAC,OAAO,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC,CAAC;YAC/E,CAAC;iBAAM,CAAC;gBACN,OAAO,SAAS,CAAC;YACnB,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;;OAMG;IACI,MAAM,CAAC,eAAe,CAAC,UAAkB,EAAE,OAAsC;QACtF,OAAO,UAAU,CAAC,cAAc,CAAC,GAAG,EAAE;YACpC,OAAO,mCACF,2BAA2B,GAC3B,OAAO,CACX,CAAC;YAEF,MAAM,aAAa,GAAiB,GAAG,CAAC,WAAW,CAAC,UAAU,EAAE,EAAE,aAAa,EAAE,IAAI,EAAE,CAAC,CAAC;YACzF,IAAI,OAAO,CAAC,aAAa,EAAE,CAAC;gBAC1B,OAAO,aAAa,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,EAAE;oBACvC,WAAW,CAAC,IAAI,GAAG,UAAU,CAAC,OAAO,CAAC,UAAU,EAAE,WAAW,CAAC,IAAI,CAAC,CAAC;oBACpE,OAAO,WAAW,CAAC;gBACrB,CAAC,CAAC,CAAC;YACL,CAAC;iBAAM,CAAC;gBACN,OAAO,aAAa,CAAC;YACvB,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,KAAK,CAAC,oBAAoB,CACtC,UAAkB,EAClB,OAAsC;QAEtC,OAAO,MAAM,UAAU,CAAC,mBAAmB,CAAC,KAAK,IAAI,EAAE;YACrD,OAAO,mCACF,2BAA2B,GAC3B,OAAO,CACX,CAAC;YAEF,MAAM,aAAa,GAAiB,MAAM,UAAU,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,aAAa,EAAE,IAAI,EAAE,CAAC,CAAC;YAClG,IAAI,OAAO,CAAC,aAAa,EAAE,CAAC;gBAC1B,OAAO,aAAa,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,EAAE;oBACvC,WAAW,CAAC,IAAI,GAAG,UAAU,CAAC,OAAO,CAAC,UAAU,EAAE,WAAW,CAAC,IAAI,CAAC,CAAC;oBACpE,OAAO,WAAW,CAAC;gBACrB,CAAC,CAAC,CAAC;YACL,CAAC;iBAAM,CAAC;gBACN,OAAO,aAAa,CAAC;YACvB,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;;OAMG;IACI,MAAM,CAAC,YAAY,CAAC,UAAkB;QAC3C,UAAU,CAAC,cAAc,CAAC,GAAG,EAAE;YAC7B,GAAG,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;QAC7B,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,KAAK,CAAC,iBAAiB,CAAC,UAAkB;QACtD,MAAM,UAAU,CAAC,mBAAmB,CAAC,GAAG,EAAE;YACxC,OAAO,GAAG,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;QAChC,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;OAOG;IACI,MAAM,CAAC,iBAAiB,CAAC,UAAkB;QAChD,UAAU,CAAC,cAAc,CAAC,GAAG,EAAE;YAC7B,GAAG,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;QAC/B,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,KAAK,CAAC,sBAAsB,CAAC,UAAkB;QAC3D,MAAM,UAAU,CAAC,mBAAmB,CAAC,GAAG,EAAE;YACxC,OAAO,GAAG,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC;IACL,CAAC;IAED,kBAAkB;IAClB,kBAAkB;IAClB,kBAAkB;IAElB;;;;;;;;OAQG;IACI,MAAM,CAAC,SAAS,CACrB,QAAgB,EAChB,QAAyB,EACzB,OAAqC;QAErC,UAAU,CAAC,cAAc,CAAC,GAAG,EAAE;YAC7B,OAAO,mCACF,0BAA0B,GAC1B,OAAO,CACX,CAAC;YAEF,IAAI,OAAO,CAAC,kBAAkB,EAAE,CAAC;gBAC/B,QAAQ,GAAG,WAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,OAAO,CAAC,kBAAkB,CAAC,CAAC;YAC7E,CAAC;YAED,IAAI,CAAC;gBACH,GAAG,CAAC,aAAa,CAAC,QAAQ,EAAE,QAAQ,EAAE,EAAE,QAAQ,EAAE,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC;YACxE,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAI,OAAO,CAAC,kBAAkB,EAAE,CAAC;oBAC/B,IAAI,CAAC,UAAU,CAAC,eAAe,CAAC,KAAc,CAAC,EAAE,CAAC;wBAChD,MAAM,KAAK,CAAC;oBACd,CAAC;oBAED,MAAM,UAAU,GAAW,UAAU,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;oBACxD,UAAU,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;oBACpC,GAAG,CAAC,aAAa,CAAC,QAAQ,EAAE,QAAQ,EAAE,EAAE,QAAQ,EAAE,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC;gBACxE,CAAC;qBAAM,CAAC;oBACN,MAAM,KAAK,CAAC;gBACd,CAAC;YACH,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;;;;OAYG;IACI,MAAM,CAAC,kBAAkB,CAC9B,QAAgB,EAChB,QAA+C,EAC/C,OAA2C;QAE3C,UAAU,CAAC,cAAc,CAAC,GAAG,EAAE;YAC7B,mEAAmE;YACnE,sEAAsE;YACtE,MAAM,MAAM,GAA6B,CAAC,GAAG,QAAQ,CAAC,CAAC;YAEvD,IAAI,EAAsB,CAAC;YAC3B,IAAI,CAAC;gBACH,EAAE,GAAG,GAAG,CAAC,QAAQ,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC;YACnC,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAI,CAAC,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,kBAAkB,CAAA,IAAI,CAAC,UAAU,CAAC,eAAe,CAAC,KAAc,CAAC,EAAE,CAAC;oBAChF,MAAM,KAAK,CAAC;gBACd,CAAC;gBAED,MAAM,UAAU,GAAW,UAAU,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;gBACxD,UAAU,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;gBACpC,EAAE,GAAG,GAAG,CAAC,QAAQ,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC;YACnC,CAAC;YAED,IAAI,CAAC;gBACH,2EAA2E;gBAC3E,wDAAwD;gBACxD,OAAO,MAAM,CAAC,MAAM,EAAE,CAAC;oBACrB,IAAI,YAAY,GAAW,GAAG,CAAC,UAAU,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;oBACtD,IAAI,cAAc,GAAW,CAAC,CAAC;oBAC/B,OAAO,cAAc,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;wBACtC,MAAM,oBAAoB,GAAW,MAAM,CAAC,cAAc,CAAC,CAAC,UAAU,CAAC;wBACvE,IAAI,YAAY,GAAG,oBAAoB,EAAE,CAAC;4BACxC,qCAAqC;4BACrC,MAAM,aAAa,GAA2B,MAAM,CAAC,cAAc,CAAC,CAAC;4BACrE,MAAM,CAAC,cAAc,CAAC,GAAG,IAAI,UAAU,CACrC,aAAa,CAAC,MAAM,EACpB,aAAa,CAAC,UAAU,GAAG,YAAY,EACvC,aAAa,CAAC,UAAU,GAAG,YAAY,CACxC,CAAC;4BACF,MAAM;wBACR,CAAC;wBACD,YAAY,IAAI,oBAAoB,CAAC;wBACrC,cAAc,EAAE,CAAC;oBACnB,CAAC;oBAED,IAAI,cAAc,GAAG,CAAC,EAAE,CAAC;wBACvB,qDAAqD;wBACrD,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,cAAc,CAAC,CAAC;oBACnC,CAAC;gBACH,CAAC;YACH,CAAC;oBAAS,CAAC;gBACT,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;YACpB,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,KAAK,CAAC,cAAc,CAChC,QAAgB,EAChB,QAAyB,EACzB,OAAqC;QAErC,MAAM,UAAU,CAAC,mBAAmB,CAAC,KAAK,IAAI,EAAE;YAC9C,OAAO,mCACF,0BAA0B,GAC1B,OAAO,CACX,CAAC;YAEF,IAAI,OAAO,CAAC,kBAAkB,EAAE,CAAC;gBAC/B,QAAQ,GAAG,WAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,OAAO,CAAC,kBAAkB,CAAC,CAAC;YAC7E,CAAC;YAED,IAAI,CAAC;gBACH,MAAM,GAAG,CAAC,SAAS,CAAC,QAAQ,EAAE,QAAQ,EAAE,EAAE,QAAQ,EAAE,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC;YAC1E,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAI,OAAO,CAAC,kBAAkB,EAAE,CAAC;oBAC/B,IAAI,CAAC,UAAU,CAAC,eAAe,CAAC,KAAc,CAAC,EAAE,CAAC;wBAChD,MAAM,KAAK,CAAC;oBACd,CAAC;oBAED,MAAM,UAAU,GAAW,UAAU,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;oBACxD,MAAM,UAAU,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;oBAC/C,MAAM,GAAG,CAAC,SAAS,CAAC,QAAQ,EAAE,QAAQ,EAAE,EAAE,QAAQ,EAAE,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC;gBAC1E,CAAC;qBAAM,CAAC;oBACN,MAAM,KAAK,CAAC;gBACd,CAAC;YACH,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,KAAK,CAAC,uBAAuB,CACzC,QAAgB,EAChB,QAA+C,EAC/C,OAA2C;QAE3C,MAAM,UAAU,CAAC,mBAAmB,CAAC,KAAK,IAAI,EAAE;YAC9C,mEAAmE;YACnE,sEAAsE;YACtE,MAAM,MAAM,GAA6B,CAAC,GAAG,QAAQ,CAAC,CAAC;YAEvD,IAAI,MAAyC,CAAC;YAC9C,IAAI,CAAC;gBACH,MAAM,GAAG,MAAM,UAAU,CAAC,IAAI,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC;YAChD,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAI,CAAC,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,kBAAkB,CAAA,IAAI,CAAC,UAAU,CAAC,eAAe,CAAC,KAAc,CAAC,EAAE,CAAC;oBAChF,MAAM,KAAK,CAAC;gBACd,CAAC;gBAED,MAAM,UAAU,GAAW,UAAU,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;gBACxD,MAAM,UAAU,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;gBAC/C,MAAM,GAAG,MAAM,UAAU,CAAC,IAAI,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC;YAChD,CAAC;YAED,IAAI,CAAC;gBACH,2EAA2E;gBAC3E,wDAAwD;gBACxD,OAAO,MAAM,CAAC,MAAM,EAAE,CAAC;oBACrB,IAAI,YAAY,GAAW,CAAC,MAAM,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,YAAY,CAAC;oBACtE,IAAI,cAAc,GAAW,CAAC,CAAC;oBAC/B,OAAO,cAAc,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;wBACtC,MAAM,oBAAoB,GAAW,MAAM,CAAC,cAAc,CAAC,CAAC,UAAU,CAAC;wBACvE,IAAI,YAAY,GAAG,oBAAoB,EAAE,CAAC;4BACxC,qCAAqC;4BACrC,MAAM,aAAa,GAA2B,MAAM,CAAC,cAAc,CAAC,CAAC;4BACrE,MAAM,CAAC,cAAc,CAAC,GAAG,IAAI,UAAU,CACrC,aAAa,CAAC,MAAM,EACpB,aAAa,CAAC,UAAU,GAAG,YAAY,EACvC,aAAa,CAAC,UAAU,GAAG,YAAY,CACxC,CAAC;4BACF,MAAM;wBACR,CAAC;wBACD,YAAY,IAAI,oBAAoB,CAAC;wBACrC,cAAc,EAAE,CAAC;oBACnB,CAAC;oBAED,IAAI,cAAc,GAAG,CAAC,EAAE,CAAC;wBACvB,qDAAqD;wBACrD,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,cAAc,CAAC,CAAC;oBACnC,CAAC;gBACH,CAAC;YACH,CAAC;oBAAS,CAAC;gBACT,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC;YACvB,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;OAQG;IACI,MAAM,CAAC,YAAY,CACxB,QAAgB,EAChB,QAAyB,EACzB,OAAqC;QAErC,UAAU,CAAC,cAAc,CAAC,GAAG,EAAE;YAC7B,OAAO,mCACF,8BAA8B,GAC9B,OAAO,CACX,CAAC;YAEF,IAAI,OAAO,CAAC,kBAAkB,EAAE,CAAC;gBAC/B,QAAQ,GAAG,WAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,OAAO,CAAC,kBAAkB,CAAC,CAAC;YAC7E,CAAC;YAED,IAAI,CAAC;gBACH,GAAG,CAAC,cAAc,CAAC,QAAQ,EAAE,QAAQ,EAAE,EAAE,QAAQ,EAAE,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC;YACzE,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAI,OAAO,CAAC,kBAAkB,EAAE,CAAC;oBAC/B,IAAI,CAAC,UAAU,CAAC,eAAe,CAAC,KAAc,CAAC,EAAE,CAAC;wBAChD,MAAM,KAAK,CAAC;oBACd,CAAC;oBAED,MAAM,UAAU,GAAW,UAAU,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;oBACxD,UAAU,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;oBACpC,GAAG,CAAC,cAAc,CAAC,QAAQ,EAAE,QAAQ,EAAE,EAAE,QAAQ,EAAE,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC;gBACzE,CAAC;qBAAM,CAAC;oBACN,MAAM,KAAK,CAAC;gBACd,CAAC;YACH,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,KAAK,CAAC,iBAAiB,CACnC,QAAgB,EAChB,QAAyB,EACzB,OAAqC;QAErC,MAAM,UAAU,CAAC,mBAAmB,CAAC,KAAK,IAAI,EAAE;YAC9C,OAAO,mCACF,8BAA8B,GAC9B,OAAO,CACX,CAAC;YAEF,IAAI,OAAO,CAAC,kBAAkB,EAAE,CAAC;gBAC/B,QAAQ,GAAG,WAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,OAAO,CAAC,kBAAkB,CAAC,CAAC;YAC7E,CAAC;YAED,IAAI,CAAC;gBACH,MAAM,GAAG,CAAC,UAAU,CAAC,QAAQ,EAAE,QAAQ,EAAE,EAAE,QAAQ,EAAE,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC;YAC3E,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAI,OAAO,CAAC,kBAAkB,EAAE,CAAC;oBAC/B,IAAI,CAAC,UAAU,CAAC,eAAe,CAAC,KAAc,CAAC,EAAE,CAAC;wBAChD,MAAM,KAAK,CAAC;oBACd,CAAC;oBAED,MAAM,UAAU,GAAW,UAAU,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;oBACxD,MAAM,UAAU,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;oBAC/C,MAAM,GAAG,CAAC,UAAU,CAAC,QAAQ,EAAE,QAAQ,EAAE,EAAE,QAAQ,EAAE,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC;gBAC3E,CAAC;qBAAM,CAAC;oBACN,MAAM,KAAK,CAAC;gBACd,CAAC;YACH,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;OAKG;IACI,MAAM,CAAC,QAAQ,CAAC,QAAgB,EAAE,OAAoC;QAC3E,OAAO,UAAU,CAAC,cAAc,CAAC,GAAG,EAAE;YACpC,OAAO,mCACF,yBAAyB,GACzB,OAAO,CACX,CAAC;YAEF,IAAI,QAAQ,GAAW,UAAU,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;YACxF,IAAI,OAAO,CAAC,kBAAkB,EAAE,CAAC;gBAC/B,QAAQ,GAAG,WAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,OAAO,CAAC,kBAAkB,CAAC,CAAC;YAClE,CAAC;YAED,OAAO,QAAQ,CAAC;QAClB,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,KAAK,CAAC,aAAa,CAAC,QAAgB,EAAE,OAAoC;QACtF,OAAO,MAAM,UAAU,CAAC,mBAAmB,CAAC,KAAK,IAAI,EAAE;YACrD,OAAO,mCACF,yBAAyB,GACzB,OAAO,CACX,CAAC;YAEF,IAAI,QAAQ,GAAW,CAAC,MAAM,UAAU,CAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;YACrG,IAAI,OAAO,CAAC,kBAAkB,EAAE,CAAC;gBAC/B,QAAQ,GAAG,WAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,OAAO,CAAC,kBAAkB,CAAC,CAAC;YAClE,CAAC;YAED,OAAO,QAAQ,CAAC;QAClB,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;OAIG;IACI,MAAM,CAAC,gBAAgB,CAAC,QAAgB;QAC7C,OAAO,UAAU,CAAC,cAAc,CAAC,GAAG,EAAE;YACpC,OAAO,GAAG,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,KAAK,CAAC,qBAAqB,CAAC,QAAgB;QACxD,OAAO,MAAM,UAAU,CAAC,mBAAmB,CAAC,GAAG,EAAE;YAC/C,OAAO,GAAG,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QAChC,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;OASG;IACI,MAAM,CAAC,QAAQ,CAAC,OAAmC;QACxD,OAAO,mCACF,yBAAyB,GACzB,OAAO,CACX,CAAC;QAEF,IAAI,UAAU,CAAC,aAAa,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,WAAW,EAAE,EAAE,CAAC;YAC/D,MAAM,IAAI,KAAK,CACb,gFAAgF,GAAG,OAAO,CAAC,UAAU,CACtG,CAAC;QACJ,CAAC;QAED,UAAU,CAAC,cAAc,CAAC,GAAG,EAAE;YAC7B,GAAG,CAAC,QAAQ,CAAC,OAAO,CAAC,UAAU,EAAE,OAAO,CAAC,eAAe,EAAE;gBACxD,YAAY,EAAE,OAAO,CAAC,qBAAqB,KAAK,qBAAqB,CAAC,KAAK;gBAC3E,SAAS,EAAE,OAAO,CAAC,qBAAqB,KAAK,qBAAqB,CAAC,SAAS;aAC7E,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,KAAK,CAAC,aAAa,CAAC,OAAmC;QACnE,OAAO,mCACF,yBAAyB,GACzB,OAAO,CACX,CAAC;QAEF,IAAI,CAAC,MAAM,UAAU,CAAC,kBAAkB,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,WAAW,EAAE,EAAE,CAAC;YAC5E,MAAM,IAAI,KAAK,CACb,gFAAgF,GAAG,OAAO,CAAC,UAAU,CACtG,CAAC;QACJ,CAAC;QAED,MAAM,UAAU,CAAC,mBAAmB,CAAC,GAAG,EAAE;YACxC,OAAO,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,OAAO,CAAC,eAAe,EAAE;gBAC3D,YAAY,EAAE,OAAO,CAAC,qBAAqB,KAAK,qBAAqB,CAAC,KAAK;gBAC3E,SAAS,EAAE,OAAO,CAAC,qBAAqB,KAAK,qBAAqB,CAAC,SAAS;aAC7E,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;OASG;IACI,MAAM,CAAC,SAAS,CAAC,OAAoC;QAC1D,OAAO,mCACF,0BAA0B,GAC1B,OAAO,CACX,CAAC;QAEF,UAAU,CAAC,cAAc,CAAC,GAAG,EAAE;YAC7B,GAAG,CAAC,QAAQ,CAAC,OAAO,CAAC,UAAU,EAAE,OAAO,CAAC,eAAe,EAAE;gBACxD,WAAW,EAAE,CAAC,CAAC,OAAO,CAAC,mBAAmB;gBAC1C,YAAY,EAAE,OAAO,CAAC,qBAAqB,KAAK,qBAAqB,CAAC,KAAK;gBAC3E,SAAS,EAAE,OAAO,CAAC,qBAAqB,KAAK,qBAAqB,CAAC,SAAS;gBAC5E,kBAAkB,EAAE,CAAC,CAAC,OAAO,CAAC,kBAAkB;gBAChD,MAAM,EAAE,OAAO,CAAC,MAAM;aACvB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,KAAK,CAAC,cAAc,CAAC,OAAyC;QAC1E,OAAO,mCACF,0BAA0B,GAC1B,OAAO,CACX,CAAC;QAEF,MAAM,UAAU,CAAC,mBAAmB,CAAC,KAAK,IAAI,EAAE;YAC9C,MAAM,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,OAAO,CAAC,eAAe,EAAE;gBAC1D,WAAW,EAAE,CAAC,CAAC,OAAO,CAAC,mBAAmB;gBAC1C,YAAY,EAAE,OAAO,CAAC,qBAAqB,KAAK,qBAAqB,CAAC,KAAK;gBAC3E,SAAS,EAAE,OAAO,CAAC,qBAAqB,KAAK,qBAAqB,CAAC,SAAS;gBAC5E,kBAAkB,EAAE,CAAC,CAAC,OAAO,CAAC,kBAAkB;gBAChD,MAAM,EAAE,OAAO,CAAC,MAAM;aACvB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;OAKG;IACI,MAAM,CAAC,UAAU,CAAC,QAAgB,EAAE,OAAsC;QAC/E,UAAU,CAAC,cAAc,CAAC,GAAG,EAAE;YAC7B,OAAO,mCACF,2BAA2B,GAC3B,OAAO,CACX,CAAC;YAEF,IAAI,CAAC;gBACH,GAAG,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;YAC3B,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAI,OAAO,CAAC,gBAAgB,IAAI,CAAC,UAAU,CAAC,eAAe,CAAC,KAAc,CAAC,EAAE,CAAC;oBAC5E,MAAM,KAAK,CAAC;gBACd,CAAC;YACH,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,KAAK,CAAC,eAAe,CACjC,QAAgB,EAChB,OAAsC;QAEtC,MAAM,UAAU,CAAC,mBAAmB,CAAC,KAAK,IAAI,EAAE;YAC9C,OAAO,mCACF,2BAA2B,GAC3B,OAAO,CACX,CAAC;YAEF,IAAI,CAAC;gBACH,MAAM,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YAC7B,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAI,OAAO,CAAC,gBAAgB,IAAI,CAAC,UAAU,CAAC,eAAe,CAAC,KAAc,CAAC,EAAE,CAAC;oBAC5E,MAAM,KAAK,CAAC;gBACd,CAAC;YACH,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED,kBAAkB;IAClB,kBAAkB;IAClB,kBAAkB;IAElB;;;;OAIG;IACI,MAAM,CAAC,iBAAiB,CAAC,IAAY;QAC1C,OAAO,UAAU,CAAC,cAAc,CAAC,GAAG,EAAE;YACpC,OAAO,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QAC7B,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,KAAK,CAAC,sBAAsB,CAAC,IAAY;QACrD,OAAO,MAAM,UAAU,CAAC,mBAAmB,CAAC,GAAG,EAAE;YAC/C,OAAO,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QACzB,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;;OAUG;IACI,MAAM,CAAC,QAAQ,CAAC,IAAY;QACjC,OAAO,UAAU,CAAC,cAAc,CAAC,GAAG,EAAE;YACpC,OAAO,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;QAChC,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,KAAK,CAAC,aAAa,CAAC,IAAY;QAC5C,OAAO,MAAM,UAAU,CAAC,mBAAmB,CAAC,GAAG,EAAE;YAC/C,OAAO,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;;;;;;;;OAgBG;IACI,MAAM,CAAC,0BAA0B,CAAC,OAAqC;QAC5E,UAAU,CAAC,cAAc,CAAC,GAAG,EAAE;YAC7B,OAAO,UAAU,CAAC,WAAW,CAAC,GAAG,EAAE;gBACjC,8GAA8G;gBAC9G,OAAO,GAAG,CAAC,WAAW,CAAC,OAAO,CAAC,cAAc,EAAE,OAAO,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC;YAClF,CAAC,EAAE,OAAO,CAAC,CAAC;QACd,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,KAAK,CAAC,+BAA+B,CAAC,OAAqC;QACvF,MAAM,UAAU,CAAC,mBAAmB,CAAC,GAAG,EAAE;YACxC,OAAO,UAAU,CAAC,gBAAgB,CAAC,GAAG,EAAE;gBACtC,8GAA8G;gBAC9G,OAAO,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,cAAc,EAAE,OAAO,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC;YAC9E,CAAC,EAAE,OAAO,CAAC,CAAC;QACd,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;;;;OAYG;IACI,MAAM,CAAC,sBAAsB,CAAC,OAAqC;QACxE,UAAU,CAAC,cAAc,CAAC,GAAG,EAAE;YAC7B,OAAO,UAAU,CAAC,WAAW,CAAC,GAAG,EAAE;gBACjC,OAAO,GAAG,CAAC,WAAW,CAAC,OAAO,CAAC,cAAc,EAAE,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;YAC9E,CAAC,EAAE,OAAO,CAAC,CAAC;QACd,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,KAAK,CAAC,2BAA2B,CAAC,OAAqC;QACnF,MAAM,UAAU,CAAC,mBAAmB,CAAC,GAAG,EAAE;YACxC,OAAO,UAAU,CAAC,gBAAgB,CAAC,GAAG,EAAE;gBACtC,OAAO,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,cAAc,EAAE,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;YAC1E,CAAC,EAAE,OAAO,CAAC,CAAC;QACd,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;;;;OAYG;IACI,MAAM,CAAC,wBAAwB,CAAC,OAAqC;QAC1E,UAAU,CAAC,cAAc,CAAC,GAAG,EAAE;YAC7B,OAAO,UAAU,CAAC,WAAW,CAAC,GAAG,EAAE;gBACjC,OAAO,GAAG,CAAC,WAAW,CAAC,OAAO,CAAC,cAAc,EAAE,OAAO,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;YAC7E,CAAC,EAAE,OAAO,CAAC,CAAC;QACd,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,KAAK,CAAC,6BAA6B,CAAC,OAAqC;QACrF,MAAM,UAAU,CAAC,mBAAmB,CAAC,GAAG,EAAE;YACxC,OAAO,UAAU,CAAC,gBAAgB,CAAC,GAAG,EAAE;gBACtC,OAAO,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,cAAc,EAAE,OAAO,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;YACzE,CAAC,EAAE,OAAO,CAAC,CAAC;QACd,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;;;;;;;OAeG;IACI,MAAM,CAAC,cAAc,CAAC,OAAqC;QAChE,UAAU,CAAC,cAAc,CAAC,GAAG,EAAE;YAC7B,OAAO,UAAU,CAAC,WAAW,CAC3B,GAAG,EAAE;gBACH,OAAO,GAAG,CAAC,QAAQ,CAAC,OAAO,CAAC,cAAc,EAAE,OAAO,CAAC,WAAW,CAAC,CAAC;YACnE,CAAC,kCACI,OAAO,KAAE,mBAAmB,EAAE,IAAI,IACxC,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,KAAK,CAAC,mBAAmB,CAAC,OAAqC;QAC3E,MAAM,UAAU,CAAC,mBAAmB,CAAC,GAAG,EAAE;YACxC,OAAO,UAAU,CAAC,gBAAgB,CAChC,GAAG,EAAE;gBACH,OAAO,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,OAAO,CAAC,WAAW,CAAC,CAAC;YAC/D,CAAC,kCACI,OAAO,KAAE,mBAAmB,EAAE,IAAI,IACxC,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;OAIG;IACI,MAAM,CAAC,WAAW,CAAC,QAAgB;QACxC,OAAO,UAAU,CAAC,cAAc,CAAC,GAAG,EAAE;YACpC,OAAO,GAAG,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,KAAK,CAAC,gBAAgB,CAAC,QAAgB;QACnD,OAAO,MAAM,UAAU,CAAC,mBAAmB,CAAC,GAAG,EAAE;YAC/C,OAAO,GAAG,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QAChC,CAAC,CAAC,CAAC;IACL,CAAC;IAED,kBAAkB;IAClB,oBAAoB;IACpB,kBAAkB;IAElB;;OAEG;IACI,MAAM,CAAC,YAAY,CAAC,KAAY;QACrC,OAAO,UAAU,CAAC,gBAAgB,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,IAAI,KAAK,QAAQ,CAAC;IACvE,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,eAAe,CAAC,KAAY;QACxC,OAAO,UAAU,CAAC,uBAAuB,CAAC,KAAK,CAAC,IAAI,UAAU,CAAC,yBAAyB,CAAC,KAAK,CAAC,CAAC;IAClG,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,uBAAuB,CAAC,KAAY;QAChD,OAAO,UAAU,CAAC,gBAAgB,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,IAAI,KAAK,QAAQ,CAAC;IACvE,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,yBAAyB,CAAC,KAAY;QAClD,OAAO,UAAU,CAAC,gBAAgB,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,IAAI,KAAK,SAAS,CAAC;IACxE,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,gBAAgB,CAAC,KAAY;QACzC,OAAO,UAAU,CAAC,gBAAgB,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,IAAI,KAAK,QAAQ,CAAC;IACvE,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,mBAAmB,CAAC,KAAY;QAC5C,OAAO,UAAU,CAAC,gBAAgB,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,IAAI,KAAK,SAAS,CAAC;IACxE,CAAC;IAED;;;OAGG;IACI,MAAM,CAAC,yBAAyB,CAAC,KAAY;QAClD,OAAO,UAAU,CAAC,gBAAgB,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,IAAI,KAAK,OAAO,IAAI,KAAK,CAAC,OAAO,KAAK,QAAQ,CAAC;IACpG,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,gBAAgB,CAAC,KAAY;QACzC,MAAM,UAAU,GAA0B,KAAK,CAAC;QAChD,OAAO,CACL,OAAO,UAAU,CAAC,IAAI,KAAK,QAAQ;YACnC,OAAO,UAAU,CAAC,KAAK,KAAK,QAAQ;YACpC,OAAO,UAAU,CAAC,IAAI,KAAK,QAAQ;YACnC,OAAO,UAAU,CAAC,OAAO,KAAK,QAAQ,CACvC,CAAC;IACJ,CAAC;IAEO,MAAM,CAAC,WAAW,CAAC,MAAkB,EAAE,OAA6C;QAC1F,IAAI,CAAC;YACH,MAAM,EAAE,CAAC;QACX,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,UAAU,CAAC,YAAY,CAAC,KAAc,CAAC,EAAE,CAAC;gBAC5C,yBAAyB;gBACzB,QAAQ,OAAO,CAAC,qBAAqB,EAAE,CAAC;oBACtC,KAAK,qBAAqB,CAAC,MAAM;wBAC/B,MAAM;oBACR,KAAK,qBAAqB,CAAC,SAAS;wBAClC,8EAA8E;wBAC9E,oCAAoC;wBACpC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;wBACrC,MAAM,EAAE,CAAC;wBACT,MAAM;oBACR,KAAK,qBAAqB,CAAC,KAAK,CAAC;oBACjC;wBACE,MAAM,KAAK,CAAC;gBAChB,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,iFAAiF;gBACjF,8EAA8E;gBAC9E,kFAAkF;gBAClF,qFAAqF;gBACrF,IACE,UAAU,CAAC,eAAe,CAAC,KAAc,CAAC;oBAC1C,CAAC,CAAC,OAAO,CAAC,mBAAmB,IAAI,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,EAC3E,CAAC;oBACD,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC;oBAC3D,MAAM,EAAE,CAAC;gBACX,CAAC;qBAAM,CAAC;oBACN,MAAM,KAAK,CAAC;gBACd,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC;IAEO,MAAM,CAAC,KAAK,CAAC,gBAAgB,CACnC,MAA2B,EAC3B,OAA6C;QAE7C,IAAI,CAAC;YACH,MAAM,MAAM,EAAE,CAAC;QACjB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,UAAU,CAAC,YAAY,CAAC,KAAc,CAAC,EAAE,CAAC;gBAC5C,yBAAyB;gBACzB,QAAQ,OAAO,CAAC,qBAAqB,EAAE,CAAC;oBACtC,KAAK,qBAAqB,CAAC,MAAM;wBAC/B,MAAM;oBACR,KAAK,qBAAqB,CAAC,SAAS;wBAClC,8EAA8E;wBAC9E,oCAAoC;wBACpC,MAAM,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;wBAChD,MAAM,MAAM,EAAE,CAAC;wBACf,MAAM;oBACR,KAAK,qBAAqB,CAAC,KAAK,CAAC;oBACjC;wBACE,MAAM,KAAK,CAAC;gBAChB,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,iFAAiF;gBACjF,8EAA8E;gBAC9E,kFAAkF;gBAClF,qFAAqF;gBACrF,IACE,UAAU,CAAC,eAAe,CAAC,KAAc,CAAC;oBAC1C,CAAC,CAAC,OAAO,CAAC,mBAAmB,IAAI,CAAC,MAAM,UAAU,CAAC,WAAW,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,CAAC,EACxF,CAAC;oBACD,MAAM,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC;oBACtE,MAAM,MAAM,EAAE,CAAC;gBACjB,CAAC;qBAAM,CAAC;oBACN,MAAM,KAAK,CAAC;gBACd,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC;IAEO,MAAM,CAAC,cAAc,CAAU,EAAiB;QACtD,IAAI,CAAC;YACH,OAAO,EAAE,EAAE,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,UAAU,CAAC,mBAAmB,CAAC,KAAc,CAAC,CAAC;YAC/C,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAEO,MAAM,CAAC,KAAK,CAAC,mBAAmB,CAAU,EAA0B;QAC1E,IAAI,CAAC;YACH,OAAO,MAAM,EAAE,EAAE,CAAC;QACpB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,UAAU,CAAC,mBAAmB,CAAC,KAAc,CAAC,CAAC;YAC/C,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAEO,MAAM,CAAC,mBAAmB,CAAC,KAAY;QAC7C,IAAI,UAAU,CAAC,gBAAgB,CAAC,KAAK,CAAC,EAAE,CAAC;YACvC,IAAI,UAAU,CAAC,uBAAuB,CAAC,KAAK,CAAC,EAAE,CAAC;gBAC9C,8DAA8D;gBAC9D,KAAK,CAAC,OAAO,GAAG,wBAAwB,KAAK,CAAC,IAAI,KAAK,KAAK,CAAC,OAAO,EAAE,CAAC;YACzE,CAAC;iBAAM,IAAI,UAAU,CAAC,yBAAyB,CAAC,KAAK,CAAC,EAAE,CAAC;gBACvD,8DAA8D;gBAC9D,KAAK,CAAC,OAAO,GAAG,0BAA0B,KAAK,CAAC,IAAI,KAAK,KAAK,CAAC,OAAO,EAAE,CAAC;YAC3E,CAAC;iBAAM,IAAI,UAAU,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE,CAAC;gBAC1C,uFAAuF;gBACvF,gGAAgG;gBAChG,MAAM,aAAa,GAA8C,KAAK,CAAC;gBACvE,8DAA8D;gBAC9D,KAAK,CAAC,OAAO,GAAG,kCAAkC,aAAa,CAAC,IAAI,KAAK,KAAK,CAAC,OAAO,EAAE,CAAC;YAC3F,CAAC;iBAAM,IAAI,UAAU,CAAC,yBAAyB,CAAC,KAAK,CAAC,EAAE,CAAC;gBACvD,8DAA8D;gBAC9D,KAAK,CAAC,OAAO,GAAG,wCAAwC,KAAK,CAAC,IAAI,KAAK,KAAK,CAAC,OAAO,EAAE,CAAC;YACzF,CAAC;iBAAM,IAAI,UAAU,CAAC,gBAAgB,CAAC,KAAK,CAAC,EAAE,CAAC;gBAC9C,8DAA8D;gBAC9D,KAAK,CAAC,OAAO,GAAG,mCAAmC,KAAK,CAAC,IAAI,KAAK,KAAK,CAAC,OAAO,EAAE,CAAC;YACpF,CAAC;iBAAM,IAAI,UAAU,CAAC,mBAAmB,CAAC,KAAK,CAAC,EAAE,CAAC;gBACjD,8DAA8D;gBAC9D,KAAK,CAAC,OAAO,GAAG,2BAA2B,KAAK,CAAC,IAAI,KAAK,KAAK,CAAC,OAAO,EAAE,CAAC;YAC5E,CAAC;QACH,CAAC;IACH,CAAC;CACF;AApvCD,gCAovCC", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\n// See <PERSON><PERSON><PERSON><PERSON> in the project root for license information.\n\nimport * as nodeJsPath from 'path';\nimport * as fs from 'fs';\nimport * as fsPromises from 'fs/promises';\nimport * as fsx from 'fs-extra';\n\nimport { Text, type NewlineKind, Encoding } from './Text';\nimport { PosixModeBits } from './PosixModeBits';\n\n/**\n * An alias for the Node.js `fs.Stats` object.\n *\n * @remarks\n * This avoids the need to import the `fs` package when using the {@link FileSystem} API.\n * @public\n */\nexport type FileSystemStats = fs.Stats;\n\n/**\n * An alias for the Node.js `fs.Dirent` object.\n *\n * @remarks\n * This avoids the need to import the `fs` package when using the {@link FileSystem} API.\n * @public\n */\nexport type FolderItem = fs.Dirent;\n\n// The PosixModeBits are intended to be used with bitwise operations.\n/* eslint-disable no-bitwise */\n\n/**\n * The options for {@link FileSystem.readFolderItems} and {@link FileSystem.readFolderItemNames}.\n * @public\n */\nexport interface IFileSystemReadFolderOptions {\n  /**\n   * If true, returns the absolute paths of the files in the folder.\n   * @defaultValue false\n   */\n  absolutePaths?: boolean;\n}\n\n/**\n * The options for {@link FileSystem.writeBuffersToFile}\n * @public\n */\nexport interface IFileSystemWriteBinaryFileOptions {\n  /**\n   * If true, will ensure the folder is created before writing the file.\n   * @defaultValue false\n   */\n  ensureFolderExists?: boolean;\n}\n\n/**\n * The options for {@link FileSystem.writeFile}\n * @public\n */\nexport interface IFileSystemWriteFileOptions extends IFileSystemWriteBinaryFileOptions {\n  /**\n   * If specified, will normalize line endings to the specified style of newline.\n   * @defaultValue `undefined` which means no conversion will be performed\n   */\n  convertLineEndings?: NewlineKind;\n\n  /**\n   * If specified, will change the encoding of the file that will be written.\n   * @defaultValue \"utf8\"\n   */\n  encoding?: Encoding;\n}\n\n/**\n * The options for {@link FileSystem.readFile}\n * @public\n */\nexport interface IFileSystemReadFileOptions {\n  /**\n   * If specified, will change the encoding of the file that will be written.\n   * @defaultValue Encoding.Utf8\n   */\n  encoding?: Encoding;\n\n  /**\n   * If specified, will normalize line endings to the specified style of newline.\n   * @defaultValue `undefined` which means no conversion will be performed\n   */\n  convertLineEndings?: NewlineKind;\n}\n\n/**\n * The options for {@link FileSystem.move}\n * @public\n */\nexport interface IFileSystemMoveOptions {\n  /**\n   * The path of the existing object to be moved.\n   * The path may be absolute or relative.\n   */\n  sourcePath: string;\n\n  /**\n   * The new path for the object.\n   * The path may be absolute or relative.\n   */\n  destinationPath: string;\n\n  /**\n   * If true, will overwrite the file if it already exists.\n   * @defaultValue true\n   */\n  overwrite?: boolean;\n\n  /**\n   * If true, will ensure the folder is created before writing the file.\n   * @defaultValue false\n   */\n  ensureFolderExists?: boolean;\n}\n\n/**\n * @public\n */\nexport interface IFileSystemCopyFileBaseOptions {\n  /**\n   * The path of the existing object to be copied.\n   * The path may be absolute or relative.\n   */\n  sourcePath: string;\n\n  /**\n   * Specifies what to do if the destination path already exists.\n   * @defaultValue {@link AlreadyExistsBehavior.Overwrite}\n   */\n  alreadyExistsBehavior?: AlreadyExistsBehavior;\n}\n\n/**\n * The options for {@link FileSystem.copyFile}\n * @public\n */\nexport interface IFileSystemCopyFileOptions extends IFileSystemCopyFileBaseOptions {\n  /**\n   * The path that the object will be copied to.\n   * The path may be absolute or relative.\n   */\n  destinationPath: string;\n}\n\n/**\n * Specifies the behavior of APIs such as {@link FileSystem.copyFile} or\n * {@link FileSystem.createSymbolicLinkFile} when the output file path already exists.\n *\n * @remarks\n * For {@link FileSystem.copyFile} and related APIs, the \"output file path\" is\n * {@link IFileSystemCopyFileOptions.destinationPath}.\n *\n * For {@link FileSystem.createSymbolicLinkFile} and related APIs, the \"output file path\" is\n * {@link IFileSystemCreateLinkOptions.newLinkPath}.\n *\n * @public\n */\nexport enum AlreadyExistsBehavior {\n  /**\n   * If the output file path already exists, try to overwrite the existing object.\n   *\n   * @remarks\n   * If overwriting the object would require recursively deleting a folder tree,\n   * then the operation will fail.  As an example, suppose {@link FileSystem.copyFile}\n   * is copying a single file `/a/b/c` to the destination path `/d/e`, and `/d/e` is a\n   * nonempty folder.  In this situation, an error will be reported; specifying\n   * `AlreadyExistsBehavior.Overwrite` does not help.  Empty folders can be overwritten\n   * depending on the details of the implementation.\n   */\n  Overwrite = 'overwrite',\n\n  /**\n   * If the output file path already exists, the operation will fail, and an error\n   * will be reported.\n   */\n  Error = 'error',\n\n  /**\n   * If the output file path already exists, skip this item, and continue the operation.\n   */\n  Ignore = 'ignore'\n}\n\n/**\n * Callback function type for {@link IFileSystemCopyFilesAsyncOptions.filter}\n * @public\n */\nexport type FileSystemCopyFilesAsyncFilter = (\n  sourcePath: string,\n  destinationPath: string\n) => Promise<boolean>;\n\n/**\n * Callback function type for {@link IFileSystemCopyFilesOptions.filter}\n * @public\n */\nexport type FileSystemCopyFilesFilter = (sourcePath: string, destinationPath: string) => boolean;\n\n/**\n * The options for {@link FileSystem.copyFilesAsync}\n * @public\n */\nexport interface IFileSystemCopyFilesAsyncOptions {\n  /**\n   * The starting path of the file or folder to be copied.\n   * The path may be absolute or relative.\n   */\n  sourcePath: string;\n\n  /**\n   * The path that the files will be copied to.\n   * The path may be absolute or relative.\n   */\n  destinationPath: string;\n\n  /**\n   * If true, then when copying symlinks, copy the target object instead of copying the link.\n   */\n  dereferenceSymlinks?: boolean;\n\n  /**\n   * Specifies what to do if a destination path already exists.\n   *\n   * @remarks\n   * This setting is applied individually for each file being copied.\n   * For example, `AlreadyExistsBehavior.Overwrite` will not recursively delete a folder\n   * whose path corresponds to an individual file that is being copied to that location.\n   */\n  alreadyExistsBehavior?: AlreadyExistsBehavior;\n\n  /**\n   * If true, then the target object will be assigned \"last modification\" and \"last access\" timestamps\n   * that are the same as the source.  Otherwise, the OS default timestamps are assigned.\n   */\n  preserveTimestamps?: boolean;\n\n  /**\n   * A callback that will be invoked for each path that is copied.  The callback can return `false`\n   * to cause the object to be excluded from the operation.\n   */\n  filter?: FileSystemCopyFilesAsyncFilter | FileSystemCopyFilesFilter;\n}\n\n/**\n * The options for {@link FileSystem.copyFiles}\n * @public\n */\nexport interface IFileSystemCopyFilesOptions extends IFileSystemCopyFilesAsyncOptions {\n  /**  {@inheritdoc IFileSystemCopyFilesAsyncOptions.filter} */\n  filter?: FileSystemCopyFilesFilter; // narrow the type to exclude FileSystemCopyFilesAsyncFilter\n}\n\n/**\n * The options for {@link FileSystem.deleteFile}\n * @public\n */\nexport interface IFileSystemDeleteFileOptions {\n  /**\n   * If true, will throw an exception if the file did not exist before `deleteFile()` was called.\n   * @defaultValue false\n   */\n  throwIfNotExists?: boolean;\n}\n\n/**\n * The options for {@link FileSystem.updateTimes}\n * Both times must be specified.\n * @public\n */\nexport interface IFileSystemUpdateTimeParameters {\n  /**\n   * The POSIX epoch time or Date when this was last accessed.\n   */\n  accessedTime: number | Date;\n\n  /**\n   * The POSIX epoch time or Date when this was last modified\n   */\n  modifiedTime: number | Date;\n}\n\n/**\n * The options for {@link FileSystem.createSymbolicLinkJunction}, {@link FileSystem.createSymbolicLinkFile},\n * {@link FileSystem.createSymbolicLinkFolder}, and {@link FileSystem.createHardLink}.\n *\n * @public\n */\nexport interface IFileSystemCreateLinkOptions {\n  /**\n   * The newly created symbolic link will point to `linkTargetPath` as its target.\n   */\n  linkTargetPath: string;\n\n  /**\n   * The newly created symbolic link will have this path.\n   */\n  newLinkPath: string;\n\n  /**\n   * Specifies what to do if the path to create already exists.\n   * The default is `AlreadyExistsBehavior.Error`.\n   */\n  alreadyExistsBehavior?: AlreadyExistsBehavior;\n}\n\ninterface IInternalFileSystemCreateLinkOptions extends IFileSystemCreateLinkOptions {\n  /**\n   * Specifies if the link target must exist.\n   */\n  linkTargetMustExist?: boolean;\n}\n\nconst MOVE_DEFAULT_OPTIONS: Partial<IFileSystemMoveOptions> = {\n  overwrite: true,\n  ensureFolderExists: false\n};\n\nconst READ_FOLDER_DEFAULT_OPTIONS: Partial<IFileSystemReadFolderOptions> = {\n  absolutePaths: false\n};\n\nconst WRITE_FILE_DEFAULT_OPTIONS: Partial<IFileSystemWriteFileOptions> = {\n  ensureFolderExists: false,\n  convertLineEndings: undefined,\n  encoding: Encoding.Utf8\n};\n\nconst APPEND_TO_FILE_DEFAULT_OPTIONS: Partial<IFileSystemWriteFileOptions> = {\n  ...WRITE_FILE_DEFAULT_OPTIONS\n};\n\nconst READ_FILE_DEFAULT_OPTIONS: Partial<IFileSystemReadFileOptions> = {\n  encoding: Encoding.Utf8,\n  convertLineEndings: undefined\n};\n\nconst COPY_FILE_DEFAULT_OPTIONS: Partial<IFileSystemCopyFileOptions> = {\n  alreadyExistsBehavior: AlreadyExistsBehavior.Overwrite\n};\n\nconst COPY_FILES_DEFAULT_OPTIONS: Partial<IFileSystemCopyFilesOptions> = {\n  alreadyExistsBehavior: AlreadyExistsBehavior.Overwrite\n};\n\nconst DELETE_FILE_DEFAULT_OPTIONS: Partial<IFileSystemDeleteFileOptions> = {\n  throwIfNotExists: false\n};\n\n/**\n * The FileSystem API provides a complete set of recommended operations for interacting with the file system.\n *\n * @remarks\n * We recommend to use this instead of the native `fs` API, because `fs` is a minimal set of low-level\n * primitives that must be mapped for each supported operating system. The FileSystem API takes a\n * philosophical approach of providing \"one obvious way\" to do each operation. We also prefer synchronous\n * operations except in cases where there would be a clear performance benefit for using async, since synchronous\n * code is much easier to read and debug. Also, indiscriminate parallelism has been seen to actually worsen\n * performance, versus improving it.\n *\n * Note that in the documentation, we refer to \"filesystem objects\", this can be a\n * file, folder, symbolic link, hard link, directory junction, etc.\n *\n * @public\n */\nexport class FileSystem {\n  // ===============\n  // COMMON OPERATIONS\n  // ===============\n\n  /**\n   * Returns true if the path exists on disk.\n   * Behind the scenes it uses `fs.existsSync()`.\n   * @remarks\n   * There is a debate about the fact that after `fs.existsSync()` returns true,\n   * the file might be deleted before fs.readSync() is called, which would imply that everybody\n   * should catch a `readSync()` exception, and nobody should ever use `fs.existsSync()`.\n   * We find this to be unpersuasive, since \"unexceptional exceptions\" really hinder the\n   * break-on-exception debugging experience. Also, throwing/catching is generally slow.\n   * @param path - The absolute or relative path to the filesystem object.\n   */\n  public static exists(path: string): boolean {\n    return FileSystem._wrapException(() => {\n      return fsx.existsSync(path);\n    });\n  }\n\n  /**\n   * An async version of {@link FileSystem.exists}.\n   */\n  public static async existsAsync(path: string): Promise<boolean> {\n    return await FileSystem._wrapExceptionAsync(() => {\n      return new Promise<boolean>((resolve: (result: boolean) => void) => {\n        fsx.exists(path, resolve);\n      });\n    });\n  }\n\n  /**\n   * Gets the statistics for a particular filesystem object.\n   * If the path is a link, this function follows the link and returns statistics about the link target.\n   * Behind the scenes it uses `fs.statSync()`.\n   * @param path - The absolute or relative path to the filesystem object.\n   */\n  public static getStatistics(path: string): FileSystemStats {\n    return FileSystem._wrapException(() => {\n      return fsx.statSync(path);\n    });\n  }\n\n  /**\n   * An async version of {@link FileSystem.getStatistics}.\n   */\n  public static async getStatisticsAsync(path: string): Promise<FileSystemStats> {\n    return await FileSystem._wrapExceptionAsync(() => {\n      return fsx.stat(path);\n    });\n  }\n\n  /**\n   * Updates the accessed and modified timestamps of the filesystem object referenced by path.\n   * Behind the scenes it uses `fs.utimesSync()`.\n   * The caller should specify both times in the `times` parameter.\n   * @param path - The path of the file that should be modified.\n   * @param times - The times that the object should be updated to reflect.\n   */\n  public static updateTimes(path: string, times: IFileSystemUpdateTimeParameters): void {\n    return FileSystem._wrapException(() => {\n      fsx.utimesSync(path, times.accessedTime, times.modifiedTime);\n    });\n  }\n\n  /**\n   * An async version of {@link FileSystem.updateTimes}.\n   */\n  public static async updateTimesAsync(path: string, times: IFileSystemUpdateTimeParameters): Promise<void> {\n    await FileSystem._wrapExceptionAsync(() => {\n      // This cast is needed because the fs-extra typings require both parameters\n      // to have the same type (number or Date), whereas Node.js does not require that.\n      return fsx.utimes(path, times.accessedTime as number, times.modifiedTime as number);\n    });\n  }\n\n  /**\n   * Changes the permissions (i.e. file mode bits) for a filesystem object.\n   * Behind the scenes it uses `fs.chmodSync()`.\n   * @param path - The absolute or relative path to the object that should be updated.\n   * @param modeBits - POSIX-style file mode bits specified using the {@link PosixModeBits} enum\n   */\n  public static changePosixModeBits(path: string, modeBits: PosixModeBits): void {\n    FileSystem._wrapException(() => {\n      fs.chmodSync(path, modeBits);\n    });\n  }\n\n  /**\n   * An async version of {@link FileSystem.changePosixModeBits}.\n   */\n  public static async changePosixModeBitsAsync(path: string, mode: PosixModeBits): Promise<void> {\n    await FileSystem._wrapExceptionAsync(() => {\n      return fsx.chmod(path, mode);\n    });\n  }\n\n  /**\n   * Retrieves the permissions (i.e. file mode bits) for a filesystem object.\n   * Behind the scenes it uses `fs.chmodSync()`.\n   * @param path - The absolute or relative path to the object that should be updated.\n   *\n   * @remarks\n   * This calls {@link FileSystem.getStatistics} to get the POSIX mode bits.\n   * If statistics in addition to the mode bits are needed, it is more efficient\n   * to call {@link FileSystem.getStatistics} directly instead.\n   */\n  public static getPosixModeBits(path: string): PosixModeBits {\n    return FileSystem._wrapException(() => {\n      return FileSystem.getStatistics(path).mode;\n    });\n  }\n\n  /**\n   * An async version of {@link FileSystem.getPosixModeBits}.\n   */\n  public static async getPosixModeBitsAsync(path: string): Promise<PosixModeBits> {\n    return await FileSystem._wrapExceptionAsync(async () => {\n      return (await FileSystem.getStatisticsAsync(path)).mode;\n    });\n  }\n\n  /**\n   * Returns a 10-character string representation of a PosixModeBits value similar to what\n   * would be displayed by a command such as \"ls -l\" on a POSIX-like operating system.\n   * @remarks\n   * For example, `PosixModeBits.AllRead | PosixModeBits.AllWrite` would be formatted as \"-rw-rw-rw-\".\n   * @param modeBits - POSIX-style file mode bits specified using the {@link PosixModeBits} enum\n   */\n  public static formatPosixModeBits(modeBits: PosixModeBits): string {\n    let result: string = '-'; // (later we may add support for additional states such as S_IFDIR or S_ISUID)\n\n    result += modeBits & PosixModeBits.UserRead ? 'r' : '-';\n    result += modeBits & PosixModeBits.UserWrite ? 'w' : '-';\n    result += modeBits & PosixModeBits.UserExecute ? 'x' : '-';\n\n    result += modeBits & PosixModeBits.GroupRead ? 'r' : '-';\n    result += modeBits & PosixModeBits.GroupWrite ? 'w' : '-';\n    result += modeBits & PosixModeBits.GroupExecute ? 'x' : '-';\n\n    result += modeBits & PosixModeBits.OthersRead ? 'r' : '-';\n    result += modeBits & PosixModeBits.OthersWrite ? 'w' : '-';\n    result += modeBits & PosixModeBits.OthersExecute ? 'x' : '-';\n\n    return result;\n  }\n\n  /**\n   * Moves a file. The folder must exist, unless the `ensureFolderExists` option is provided.\n   * Behind the scenes it uses `fs-extra.moveSync()`\n   */\n  public static move(options: IFileSystemMoveOptions): void {\n    FileSystem._wrapException(() => {\n      options = {\n        ...MOVE_DEFAULT_OPTIONS,\n        ...options\n      };\n\n      try {\n        fsx.moveSync(options.sourcePath, options.destinationPath, { overwrite: options.overwrite });\n      } catch (error) {\n        if (options.ensureFolderExists) {\n          if (!FileSystem.isNotExistError(error as Error)) {\n            throw error;\n          }\n\n          const folderPath: string = nodeJsPath.dirname(options.destinationPath);\n          FileSystem.ensureFolder(folderPath);\n          fsx.moveSync(options.sourcePath, options.destinationPath, { overwrite: options.overwrite });\n        } else {\n          throw error;\n        }\n      }\n    });\n  }\n\n  /**\n   * An async version of {@link FileSystem.move}.\n   */\n  public static async moveAsync(options: IFileSystemMoveOptions): Promise<void> {\n    await FileSystem._wrapExceptionAsync(async () => {\n      options = {\n        ...MOVE_DEFAULT_OPTIONS,\n        ...options\n      };\n\n      try {\n        await fsx.move(options.sourcePath, options.destinationPath, { overwrite: options.overwrite });\n      } catch (error) {\n        if (options.ensureFolderExists) {\n          if (!FileSystem.isNotExistError(error as Error)) {\n            throw error;\n          }\n\n          const folderPath: string = nodeJsPath.dirname(options.destinationPath);\n          await FileSystem.ensureFolderAsync(nodeJsPath.dirname(folderPath));\n          await fsx.move(options.sourcePath, options.destinationPath, { overwrite: options.overwrite });\n        } else {\n          throw error;\n        }\n      }\n    });\n  }\n\n  // ===============\n  // FOLDER OPERATIONS\n  // ===============\n\n  /**\n   * Recursively creates a folder at a given path.\n   * Behind the scenes is uses `fs-extra.ensureDirSync()`.\n   * @remarks\n   * Throws an exception if anything in the folderPath is not a folder.\n   * @param folderPath - The absolute or relative path of the folder which should be created.\n   */\n  public static ensureFolder(folderPath: string): void {\n    FileSystem._wrapException(() => {\n      fsx.ensureDirSync(folderPath);\n    });\n  }\n\n  /**\n   * An async version of {@link FileSystem.ensureFolder}.\n   */\n  public static async ensureFolderAsync(folderPath: string): Promise<void> {\n    await FileSystem._wrapExceptionAsync(() => {\n      return fsx.ensureDir(folderPath);\n    });\n  }\n\n  /**\n   * Reads the names of folder entries, not including \".\" or \"..\".\n   * Behind the scenes it uses `fs.readdirSync()`.\n   * @param folderPath - The absolute or relative path to the folder which should be read.\n   * @param options - Optional settings that can change the behavior. Type: `IReadFolderOptions`\n   */\n  public static readFolderItemNames(folderPath: string, options?: IFileSystemReadFolderOptions): string[] {\n    return FileSystem._wrapException(() => {\n      options = {\n        ...READ_FOLDER_DEFAULT_OPTIONS,\n        ...options\n      };\n\n      const fileNames: string[] = fsx.readdirSync(folderPath);\n      if (options.absolutePaths) {\n        return fileNames.map((fileName) => nodeJsPath.resolve(folderPath, fileName));\n      } else {\n        return fileNames;\n      }\n    });\n  }\n\n  /**\n   * An async version of {@link FileSystem.readFolderItemNames}.\n   */\n  public static async readFolderItemNamesAsync(\n    folderPath: string,\n    options?: IFileSystemReadFolderOptions\n  ): Promise<string[]> {\n    return await FileSystem._wrapExceptionAsync(async () => {\n      options = {\n        ...READ_FOLDER_DEFAULT_OPTIONS,\n        ...options\n      };\n\n      const fileNames: string[] = await fsx.readdir(folderPath);\n      if (options.absolutePaths) {\n        return fileNames.map((fileName) => nodeJsPath.resolve(folderPath, fileName));\n      } else {\n        return fileNames;\n      }\n    });\n  }\n\n  /**\n   * Reads the contents of the folder, not including \".\" or \"..\", returning objects including the\n   * entry names and types.\n   * Behind the scenes it uses `fs.readdirSync()`.\n   * @param folderPath - The absolute or relative path to the folder which should be read.\n   * @param options - Optional settings that can change the behavior. Type: `IReadFolderOptions`\n   */\n  public static readFolderItems(folderPath: string, options?: IFileSystemReadFolderOptions): FolderItem[] {\n    return FileSystem._wrapException(() => {\n      options = {\n        ...READ_FOLDER_DEFAULT_OPTIONS,\n        ...options\n      };\n\n      const folderEntries: FolderItem[] = fsx.readdirSync(folderPath, { withFileTypes: true });\n      if (options.absolutePaths) {\n        return folderEntries.map((folderEntry) => {\n          folderEntry.name = nodeJsPath.resolve(folderPath, folderEntry.name);\n          return folderEntry;\n        });\n      } else {\n        return folderEntries;\n      }\n    });\n  }\n\n  /**\n   * An async version of {@link FileSystem.readFolderItems}.\n   */\n  public static async readFolderItemsAsync(\n    folderPath: string,\n    options?: IFileSystemReadFolderOptions\n  ): Promise<FolderItem[]> {\n    return await FileSystem._wrapExceptionAsync(async () => {\n      options = {\n        ...READ_FOLDER_DEFAULT_OPTIONS,\n        ...options\n      };\n\n      const folderEntries: FolderItem[] = await fsPromises.readdir(folderPath, { withFileTypes: true });\n      if (options.absolutePaths) {\n        return folderEntries.map((folderEntry) => {\n          folderEntry.name = nodeJsPath.resolve(folderPath, folderEntry.name);\n          return folderEntry;\n        });\n      } else {\n        return folderEntries;\n      }\n    });\n  }\n\n  /**\n   * Deletes a folder, including all of its contents.\n   * Behind the scenes is uses `fs-extra.removeSync()`.\n   * @remarks\n   * Does not throw if the folderPath does not exist.\n   * @param folderPath - The absolute or relative path to the folder which should be deleted.\n   */\n  public static deleteFolder(folderPath: string): void {\n    FileSystem._wrapException(() => {\n      fsx.removeSync(folderPath);\n    });\n  }\n\n  /**\n   * An async version of {@link FileSystem.deleteFolder}.\n   */\n  public static async deleteFolderAsync(folderPath: string): Promise<void> {\n    await FileSystem._wrapExceptionAsync(() => {\n      return fsx.remove(folderPath);\n    });\n  }\n\n  /**\n   * Deletes the content of a folder, but not the folder itself. Also ensures the folder exists.\n   * Behind the scenes it uses `fs-extra.emptyDirSync()`.\n   * @remarks\n   * This is a workaround for a common race condition, where the virus scanner holds a lock on the folder\n   * for a brief period after it was deleted, causing EBUSY errors for any code that tries to recreate the folder.\n   * @param folderPath - The absolute or relative path to the folder which should have its contents deleted.\n   */\n  public static ensureEmptyFolder(folderPath: string): void {\n    FileSystem._wrapException(() => {\n      fsx.emptyDirSync(folderPath);\n    });\n  }\n\n  /**\n   * An async version of {@link FileSystem.ensureEmptyFolder}.\n   */\n  public static async ensureEmptyFolderAsync(folderPath: string): Promise<void> {\n    await FileSystem._wrapExceptionAsync(() => {\n      return fsx.emptyDir(folderPath);\n    });\n  }\n\n  // ===============\n  // FILE OPERATIONS\n  // ===============\n\n  /**\n   * Writes a text string to a file on disk, overwriting the file if it already exists.\n   * Behind the scenes it uses `fs.writeFileSync()`.\n   * @remarks\n   * Throws an error if the folder doesn't exist, unless ensureFolder=true.\n   * @param filePath - The absolute or relative path of the file.\n   * @param contents - The text that should be written to the file.\n   * @param options - Optional settings that can change the behavior. Type: `IWriteFileOptions`\n   */\n  public static writeFile(\n    filePath: string,\n    contents: string | Buffer,\n    options?: IFileSystemWriteFileOptions\n  ): void {\n    FileSystem._wrapException(() => {\n      options = {\n        ...WRITE_FILE_DEFAULT_OPTIONS,\n        ...options\n      };\n\n      if (options.convertLineEndings) {\n        contents = Text.convertTo(contents.toString(), options.convertLineEndings);\n      }\n\n      try {\n        fsx.writeFileSync(filePath, contents, { encoding: options.encoding });\n      } catch (error) {\n        if (options.ensureFolderExists) {\n          if (!FileSystem.isNotExistError(error as Error)) {\n            throw error;\n          }\n\n          const folderPath: string = nodeJsPath.dirname(filePath);\n          FileSystem.ensureFolder(folderPath);\n          fsx.writeFileSync(filePath, contents, { encoding: options.encoding });\n        } else {\n          throw error;\n        }\n      }\n    });\n  }\n\n  /**\n   * Writes the contents of multiple Uint8Arrays to a file on disk, overwriting the file if it already exists.\n   * Behind the scenes it uses `fs.writevSync()`.\n   *\n   * This API is useful for writing large files efficiently, especially if the input is being concatenated from\n   * multiple sources.\n   *\n   * @remarks\n   * Throws an error if the folder doesn't exist, unless ensureFolder=true.\n   * @param filePath - The absolute or relative path of the file.\n   * @param contents - The content that should be written to the file.\n   * @param options - Optional settings that can change the behavior.\n   */\n  public static writeBuffersToFile(\n    filePath: string,\n    contents: ReadonlyArray<NodeJS.ArrayBufferView>,\n    options?: IFileSystemWriteBinaryFileOptions\n  ): void {\n    FileSystem._wrapException(() => {\n      // Need a mutable copy of the iterable to handle incomplete writes,\n      // since writev() doesn't take an argument for where to start writing.\n      const toCopy: NodeJS.ArrayBufferView[] = [...contents];\n\n      let fd: number | undefined;\n      try {\n        fd = fsx.openSync(filePath, 'w');\n      } catch (error) {\n        if (!options?.ensureFolderExists || !FileSystem.isNotExistError(error as Error)) {\n          throw error;\n        }\n\n        const folderPath: string = nodeJsPath.dirname(filePath);\n        FileSystem.ensureFolder(folderPath);\n        fd = fsx.openSync(filePath, 'w');\n      }\n\n      try {\n        // In practice this loop will have exactly 1 iteration, but the spec allows\n        // for a writev call to write fewer bytes than requested\n        while (toCopy.length) {\n          let bytesWritten: number = fsx.writevSync(fd, toCopy);\n          let buffersWritten: number = 0;\n          while (buffersWritten < toCopy.length) {\n            const bytesInCurrentBuffer: number = toCopy[buffersWritten].byteLength;\n            if (bytesWritten < bytesInCurrentBuffer) {\n              // This buffer was partially written.\n              const currentToCopy: NodeJS.ArrayBufferView = toCopy[buffersWritten];\n              toCopy[buffersWritten] = new Uint8Array(\n                currentToCopy.buffer,\n                currentToCopy.byteOffset + bytesWritten,\n                currentToCopy.byteLength - bytesWritten\n              );\n              break;\n            }\n            bytesWritten -= bytesInCurrentBuffer;\n            buffersWritten++;\n          }\n\n          if (buffersWritten > 0) {\n            // Avoid cost of shifting the array more than needed.\n            toCopy.splice(0, buffersWritten);\n          }\n        }\n      } finally {\n        fsx.closeSync(fd);\n      }\n    });\n  }\n\n  /**\n   * An async version of {@link FileSystem.writeFile}.\n   */\n  public static async writeFileAsync(\n    filePath: string,\n    contents: string | Buffer,\n    options?: IFileSystemWriteFileOptions\n  ): Promise<void> {\n    await FileSystem._wrapExceptionAsync(async () => {\n      options = {\n        ...WRITE_FILE_DEFAULT_OPTIONS,\n        ...options\n      };\n\n      if (options.convertLineEndings) {\n        contents = Text.convertTo(contents.toString(), options.convertLineEndings);\n      }\n\n      try {\n        await fsx.writeFile(filePath, contents, { encoding: options.encoding });\n      } catch (error) {\n        if (options.ensureFolderExists) {\n          if (!FileSystem.isNotExistError(error as Error)) {\n            throw error;\n          }\n\n          const folderPath: string = nodeJsPath.dirname(filePath);\n          await FileSystem.ensureFolderAsync(folderPath);\n          await fsx.writeFile(filePath, contents, { encoding: options.encoding });\n        } else {\n          throw error;\n        }\n      }\n    });\n  }\n\n  /**\n   * An async version of {@link FileSystem.writeBuffersToFile}.\n   */\n  public static async writeBuffersToFileAsync(\n    filePath: string,\n    contents: ReadonlyArray<NodeJS.ArrayBufferView>,\n    options?: IFileSystemWriteBinaryFileOptions\n  ): Promise<void> {\n    await FileSystem._wrapExceptionAsync(async () => {\n      // Need a mutable copy of the iterable to handle incomplete writes,\n      // since writev() doesn't take an argument for where to start writing.\n      const toCopy: NodeJS.ArrayBufferView[] = [...contents];\n\n      let handle: fsPromises.FileHandle | undefined;\n      try {\n        handle = await fsPromises.open(filePath, 'w');\n      } catch (error) {\n        if (!options?.ensureFolderExists || !FileSystem.isNotExistError(error as Error)) {\n          throw error;\n        }\n\n        const folderPath: string = nodeJsPath.dirname(filePath);\n        await FileSystem.ensureFolderAsync(folderPath);\n        handle = await fsPromises.open(filePath, 'w');\n      }\n\n      try {\n        // In practice this loop will have exactly 1 iteration, but the spec allows\n        // for a writev call to write fewer bytes than requested\n        while (toCopy.length) {\n          let bytesWritten: number = (await handle.writev(toCopy)).bytesWritten;\n          let buffersWritten: number = 0;\n          while (buffersWritten < toCopy.length) {\n            const bytesInCurrentBuffer: number = toCopy[buffersWritten].byteLength;\n            if (bytesWritten < bytesInCurrentBuffer) {\n              // This buffer was partially written.\n              const currentToCopy: NodeJS.ArrayBufferView = toCopy[buffersWritten];\n              toCopy[buffersWritten] = new Uint8Array(\n                currentToCopy.buffer,\n                currentToCopy.byteOffset + bytesWritten,\n                currentToCopy.byteLength - bytesWritten\n              );\n              break;\n            }\n            bytesWritten -= bytesInCurrentBuffer;\n            buffersWritten++;\n          }\n\n          if (buffersWritten > 0) {\n            // Avoid cost of shifting the array more than needed.\n            toCopy.splice(0, buffersWritten);\n          }\n        }\n      } finally {\n        await handle.close();\n      }\n    });\n  }\n\n  /**\n   * Writes a text string to a file on disk, appending to the file if it already exists.\n   * Behind the scenes it uses `fs.appendFileSync()`.\n   * @remarks\n   * Throws an error if the folder doesn't exist, unless ensureFolder=true.\n   * @param filePath - The absolute or relative path of the file.\n   * @param contents - The text that should be written to the file.\n   * @param options - Optional settings that can change the behavior. Type: `IWriteFileOptions`\n   */\n  public static appendToFile(\n    filePath: string,\n    contents: string | Buffer,\n    options?: IFileSystemWriteFileOptions\n  ): void {\n    FileSystem._wrapException(() => {\n      options = {\n        ...APPEND_TO_FILE_DEFAULT_OPTIONS,\n        ...options\n      };\n\n      if (options.convertLineEndings) {\n        contents = Text.convertTo(contents.toString(), options.convertLineEndings);\n      }\n\n      try {\n        fsx.appendFileSync(filePath, contents, { encoding: options.encoding });\n      } catch (error) {\n        if (options.ensureFolderExists) {\n          if (!FileSystem.isNotExistError(error as Error)) {\n            throw error;\n          }\n\n          const folderPath: string = nodeJsPath.dirname(filePath);\n          FileSystem.ensureFolder(folderPath);\n          fsx.appendFileSync(filePath, contents, { encoding: options.encoding });\n        } else {\n          throw error;\n        }\n      }\n    });\n  }\n\n  /**\n   * An async version of {@link FileSystem.appendToFile}.\n   */\n  public static async appendToFileAsync(\n    filePath: string,\n    contents: string | Buffer,\n    options?: IFileSystemWriteFileOptions\n  ): Promise<void> {\n    await FileSystem._wrapExceptionAsync(async () => {\n      options = {\n        ...APPEND_TO_FILE_DEFAULT_OPTIONS,\n        ...options\n      };\n\n      if (options.convertLineEndings) {\n        contents = Text.convertTo(contents.toString(), options.convertLineEndings);\n      }\n\n      try {\n        await fsx.appendFile(filePath, contents, { encoding: options.encoding });\n      } catch (error) {\n        if (options.ensureFolderExists) {\n          if (!FileSystem.isNotExistError(error as Error)) {\n            throw error;\n          }\n\n          const folderPath: string = nodeJsPath.dirname(filePath);\n          await FileSystem.ensureFolderAsync(folderPath);\n          await fsx.appendFile(filePath, contents, { encoding: options.encoding });\n        } else {\n          throw error;\n        }\n      }\n    });\n  }\n\n  /**\n   * Reads the contents of a file into a string.\n   * Behind the scenes it uses `fs.readFileSync()`.\n   * @param filePath - The relative or absolute path to the file whose contents should be read.\n   * @param options - Optional settings that can change the behavior. Type: `IReadFileOptions`\n   */\n  public static readFile(filePath: string, options?: IFileSystemReadFileOptions): string {\n    return FileSystem._wrapException(() => {\n      options = {\n        ...READ_FILE_DEFAULT_OPTIONS,\n        ...options\n      };\n\n      let contents: string = FileSystem.readFileToBuffer(filePath).toString(options.encoding);\n      if (options.convertLineEndings) {\n        contents = Text.convertTo(contents, options.convertLineEndings);\n      }\n\n      return contents;\n    });\n  }\n\n  /**\n   * An async version of {@link FileSystem.readFile}.\n   */\n  public static async readFileAsync(filePath: string, options?: IFileSystemReadFileOptions): Promise<string> {\n    return await FileSystem._wrapExceptionAsync(async () => {\n      options = {\n        ...READ_FILE_DEFAULT_OPTIONS,\n        ...options\n      };\n\n      let contents: string = (await FileSystem.readFileToBufferAsync(filePath)).toString(options.encoding);\n      if (options.convertLineEndings) {\n        contents = Text.convertTo(contents, options.convertLineEndings);\n      }\n\n      return contents;\n    });\n  }\n\n  /**\n   * Reads the contents of a file into a buffer.\n   * Behind the scenes is uses `fs.readFileSync()`.\n   * @param filePath - The relative or absolute path to the file whose contents should be read.\n   */\n  public static readFileToBuffer(filePath: string): Buffer {\n    return FileSystem._wrapException(() => {\n      return fsx.readFileSync(filePath);\n    });\n  }\n\n  /**\n   * An async version of {@link FileSystem.readFileToBuffer}.\n   */\n  public static async readFileToBufferAsync(filePath: string): Promise<Buffer> {\n    return await FileSystem._wrapExceptionAsync(() => {\n      return fsx.readFile(filePath);\n    });\n  }\n\n  /**\n   * Copies a single file from one location to another.\n   * By default, destinationPath is overwritten if it already exists.\n   *\n   * @remarks\n   * The `copyFile()` API cannot be used to copy folders.  It copies at most one file.\n   * Use {@link FileSystem.copyFiles} if you need to recursively copy a tree of folders.\n   *\n   * The implementation is based on `copySync()` from the `fs-extra` package.\n   */\n  public static copyFile(options: IFileSystemCopyFileOptions): void {\n    options = {\n      ...COPY_FILE_DEFAULT_OPTIONS,\n      ...options\n    };\n\n    if (FileSystem.getStatistics(options.sourcePath).isDirectory()) {\n      throw new Error(\n        'The specified path refers to a folder; this operation expects a file object:\\n' + options.sourcePath\n      );\n    }\n\n    FileSystem._wrapException(() => {\n      fsx.copySync(options.sourcePath, options.destinationPath, {\n        errorOnExist: options.alreadyExistsBehavior === AlreadyExistsBehavior.Error,\n        overwrite: options.alreadyExistsBehavior === AlreadyExistsBehavior.Overwrite\n      });\n    });\n  }\n\n  /**\n   * An async version of {@link FileSystem.copyFile}.\n   */\n  public static async copyFileAsync(options: IFileSystemCopyFileOptions): Promise<void> {\n    options = {\n      ...COPY_FILE_DEFAULT_OPTIONS,\n      ...options\n    };\n\n    if ((await FileSystem.getStatisticsAsync(options.sourcePath)).isDirectory()) {\n      throw new Error(\n        'The specified path refers to a folder; this operation expects a file object:\\n' + options.sourcePath\n      );\n    }\n\n    await FileSystem._wrapExceptionAsync(() => {\n      return fsx.copy(options.sourcePath, options.destinationPath, {\n        errorOnExist: options.alreadyExistsBehavior === AlreadyExistsBehavior.Error,\n        overwrite: options.alreadyExistsBehavior === AlreadyExistsBehavior.Overwrite\n      });\n    });\n  }\n\n  /**\n   * Copies a file or folder from one location to another, recursively copying any folder contents.\n   * By default, destinationPath is overwritten if it already exists.\n   *\n   * @remarks\n   * If you only intend to copy a single file, it is recommended to use {@link FileSystem.copyFile}\n   * instead to more clearly communicate the intended operation.\n   *\n   * The implementation is based on `copySync()` from the `fs-extra` package.\n   */\n  public static copyFiles(options: IFileSystemCopyFilesOptions): void {\n    options = {\n      ...COPY_FILES_DEFAULT_OPTIONS,\n      ...options\n    };\n\n    FileSystem._wrapException(() => {\n      fsx.copySync(options.sourcePath, options.destinationPath, {\n        dereference: !!options.dereferenceSymlinks,\n        errorOnExist: options.alreadyExistsBehavior === AlreadyExistsBehavior.Error,\n        overwrite: options.alreadyExistsBehavior === AlreadyExistsBehavior.Overwrite,\n        preserveTimestamps: !!options.preserveTimestamps,\n        filter: options.filter\n      });\n    });\n  }\n\n  /**\n   * An async version of {@link FileSystem.copyFiles}.\n   */\n  public static async copyFilesAsync(options: IFileSystemCopyFilesAsyncOptions): Promise<void> {\n    options = {\n      ...COPY_FILES_DEFAULT_OPTIONS,\n      ...options\n    };\n\n    await FileSystem._wrapExceptionAsync(async () => {\n      await fsx.copy(options.sourcePath, options.destinationPath, {\n        dereference: !!options.dereferenceSymlinks,\n        errorOnExist: options.alreadyExistsBehavior === AlreadyExistsBehavior.Error,\n        overwrite: options.alreadyExistsBehavior === AlreadyExistsBehavior.Overwrite,\n        preserveTimestamps: !!options.preserveTimestamps,\n        filter: options.filter\n      });\n    });\n  }\n\n  /**\n   * Deletes a file. Can optionally throw if the file doesn't exist.\n   * Behind the scenes it uses `fs.unlinkSync()`.\n   * @param filePath - The absolute or relative path to the file that should be deleted.\n   * @param options - Optional settings that can change the behavior. Type: `IDeleteFileOptions`\n   */\n  public static deleteFile(filePath: string, options?: IFileSystemDeleteFileOptions): void {\n    FileSystem._wrapException(() => {\n      options = {\n        ...DELETE_FILE_DEFAULT_OPTIONS,\n        ...options\n      };\n\n      try {\n        fsx.unlinkSync(filePath);\n      } catch (error) {\n        if (options.throwIfNotExists || !FileSystem.isNotExistError(error as Error)) {\n          throw error;\n        }\n      }\n    });\n  }\n\n  /**\n   * An async version of {@link FileSystem.deleteFile}.\n   */\n  public static async deleteFileAsync(\n    filePath: string,\n    options?: IFileSystemDeleteFileOptions\n  ): Promise<void> {\n    await FileSystem._wrapExceptionAsync(async () => {\n      options = {\n        ...DELETE_FILE_DEFAULT_OPTIONS,\n        ...options\n      };\n\n      try {\n        await fsx.unlink(filePath);\n      } catch (error) {\n        if (options.throwIfNotExists || !FileSystem.isNotExistError(error as Error)) {\n          throw error;\n        }\n      }\n    });\n  }\n\n  // ===============\n  // LINK OPERATIONS\n  // ===============\n\n  /**\n   * Gets the statistics of a filesystem object. Does NOT follow the link to its target.\n   * Behind the scenes it uses `fs.lstatSync()`.\n   * @param path - The absolute or relative path to the filesystem object.\n   */\n  public static getLinkStatistics(path: string): FileSystemStats {\n    return FileSystem._wrapException(() => {\n      return fsx.lstatSync(path);\n    });\n  }\n\n  /**\n   * An async version of {@link FileSystem.getLinkStatistics}.\n   */\n  public static async getLinkStatisticsAsync(path: string): Promise<FileSystemStats> {\n    return await FileSystem._wrapExceptionAsync(() => {\n      return fsx.lstat(path);\n    });\n  }\n\n  /**\n   * If `path` refers to a symbolic link, this returns the path of the link target, which may be\n   * an absolute or relative path.\n   *\n   * @remarks\n   * If `path` refers to a filesystem object that is not a symbolic link, then an `ErrnoException` is thrown\n   * with code 'UNKNOWN'.  If `path` does not exist, then an `ErrnoException` is thrown with code `ENOENT`.\n   *\n   * @param path - The absolute or relative path to the symbolic link.\n   * @returns the path of the link target\n   */\n  public static readLink(path: string): string {\n    return FileSystem._wrapException(() => {\n      return fsx.readlinkSync(path);\n    });\n  }\n\n  /**\n   * An async version of {@link FileSystem.readLink}.\n   */\n  public static async readLinkAsync(path: string): Promise<string> {\n    return await FileSystem._wrapExceptionAsync(() => {\n      return fsx.readlink(path);\n    });\n  }\n\n  /**\n   * Creates an NTFS \"directory junction\" on Windows operating systems; for other operating systems, it\n   * creates a regular symbolic link.  The link target must be a folder, not a file.\n   * Behind the scenes it uses `fs.symlinkSync()`.\n   *\n   * @remarks\n   * For security reasons, Windows operating systems by default require administrator elevation to create\n   * symbolic links.  As a result, on Windows it's generally recommended for Node.js tools to use hard links\n   * (for files) or NTFS directory junctions (for folders), since regular users are allowed to create them.\n   * Hard links and junctions are less vulnerable to symlink attacks because they cannot reference a network share,\n   * and their target must exist at the time of link creation.  Non-Windows operating systems generally don't\n   * restrict symlink creation, and as such are more vulnerable to symlink attacks.  Note that Windows can be\n   * configured to permit regular users to create symlinks, for example by enabling Windows 10 \"developer mode.\"\n   *\n   * A directory junction requires the link source and target to both be located on local disk volumes;\n   * if not, use a symbolic link instead.\n   */\n  public static createSymbolicLinkJunction(options: IFileSystemCreateLinkOptions): void {\n    FileSystem._wrapException(() => {\n      return FileSystem._handleLink(() => {\n        // For directories, we use a Windows \"junction\".  On POSIX operating systems, this produces a regular symlink.\n        return fsx.symlinkSync(options.linkTargetPath, options.newLinkPath, 'junction');\n      }, options);\n    });\n  }\n\n  /**\n   * An async version of {@link FileSystem.createSymbolicLinkJunction}.\n   */\n  public static async createSymbolicLinkJunctionAsync(options: IFileSystemCreateLinkOptions): Promise<void> {\n    await FileSystem._wrapExceptionAsync(() => {\n      return FileSystem._handleLinkAsync(() => {\n        // For directories, we use a Windows \"junction\".  On POSIX operating systems, this produces a regular symlink.\n        return fsx.symlink(options.linkTargetPath, options.newLinkPath, 'junction');\n      }, options);\n    });\n  }\n\n  /**\n   * Creates a symbolic link to a file.  On Windows operating systems, this may require administrator elevation.\n   * Behind the scenes it uses `fs.symlinkSync()`.\n   *\n   * @remarks\n   * To avoid administrator elevation on Windows, use {@link FileSystem.createHardLink} instead.\n   *\n   * On Windows operating systems, the NTFS file system distinguishes file symlinks versus directory symlinks:\n   * If the target is not the correct type, the symlink will be created successfully, but will fail to resolve.\n   * Other operating systems do not make this distinction, in which case {@link FileSystem.createSymbolicLinkFile}\n   * and {@link FileSystem.createSymbolicLinkFolder} can be used interchangeably, but doing so will make your\n   * tool incompatible with Windows.\n   */\n  public static createSymbolicLinkFile(options: IFileSystemCreateLinkOptions): void {\n    FileSystem._wrapException(() => {\n      return FileSystem._handleLink(() => {\n        return fsx.symlinkSync(options.linkTargetPath, options.newLinkPath, 'file');\n      }, options);\n    });\n  }\n\n  /**\n   * An async version of {@link FileSystem.createSymbolicLinkFile}.\n   */\n  public static async createSymbolicLinkFileAsync(options: IFileSystemCreateLinkOptions): Promise<void> {\n    await FileSystem._wrapExceptionAsync(() => {\n      return FileSystem._handleLinkAsync(() => {\n        return fsx.symlink(options.linkTargetPath, options.newLinkPath, 'file');\n      }, options);\n    });\n  }\n\n  /**\n   * Creates a symbolic link to a folder.  On Windows operating systems, this may require administrator elevation.\n   * Behind the scenes it uses `fs.symlinkSync()`.\n   *\n   * @remarks\n   * To avoid administrator elevation on Windows, use {@link FileSystem.createSymbolicLinkJunction} instead.\n   *\n   * On Windows operating systems, the NTFS file system distinguishes file symlinks versus directory symlinks:\n   * If the target is not the correct type, the symlink will be created successfully, but will fail to resolve.\n   * Other operating systems do not make this distinction, in which case {@link FileSystem.createSymbolicLinkFile}\n   * and {@link FileSystem.createSymbolicLinkFolder} can be used interchangeably, but doing so will make your\n   * tool incompatible with Windows.\n   */\n  public static createSymbolicLinkFolder(options: IFileSystemCreateLinkOptions): void {\n    FileSystem._wrapException(() => {\n      return FileSystem._handleLink(() => {\n        return fsx.symlinkSync(options.linkTargetPath, options.newLinkPath, 'dir');\n      }, options);\n    });\n  }\n\n  /**\n   * An async version of {@link FileSystem.createSymbolicLinkFolder}.\n   */\n  public static async createSymbolicLinkFolderAsync(options: IFileSystemCreateLinkOptions): Promise<void> {\n    await FileSystem._wrapExceptionAsync(() => {\n      return FileSystem._handleLinkAsync(() => {\n        return fsx.symlink(options.linkTargetPath, options.newLinkPath, 'dir');\n      }, options);\n    });\n  }\n\n  /**\n   * Creates a hard link.  The link target must be a file, not a folder.\n   * Behind the scenes it uses `fs.linkSync()`.\n   *\n   * @remarks\n   * For security reasons, Windows operating systems by default require administrator elevation to create\n   * symbolic links.  As a result, on Windows it's generally recommended for Node.js tools to use hard links\n   * (for files) or NTFS directory junctions (for folders), since regular users are allowed to create them.\n   * Hard links and junctions are less vulnerable to symlink attacks because they cannot reference a network share,\n   * and their target must exist at the time of link creation.  Non-Windows operating systems generally don't\n   * restrict symlink creation, and as such are more vulnerable to symlink attacks.  Note that Windows can be\n   * configured to permit regular users to create symlinks, for example by enabling Windows 10 \"developer mode.\"\n   *\n   * A hard link requires the link source and target to both be located on same disk volume;\n   * if not, use a symbolic link instead.\n   */\n  public static createHardLink(options: IFileSystemCreateLinkOptions): void {\n    FileSystem._wrapException(() => {\n      return FileSystem._handleLink(\n        () => {\n          return fsx.linkSync(options.linkTargetPath, options.newLinkPath);\n        },\n        { ...options, linkTargetMustExist: true }\n      );\n    });\n  }\n\n  /**\n   * An async version of {@link FileSystem.createHardLink}.\n   */\n  public static async createHardLinkAsync(options: IFileSystemCreateLinkOptions): Promise<void> {\n    await FileSystem._wrapExceptionAsync(() => {\n      return FileSystem._handleLinkAsync(\n        () => {\n          return fsx.link(options.linkTargetPath, options.newLinkPath);\n        },\n        { ...options, linkTargetMustExist: true }\n      );\n    });\n  }\n\n  /**\n   * Follows a link to its destination and returns the absolute path to the final target of the link.\n   * Behind the scenes it uses `fs.realpathSync()`.\n   * @param linkPath - The path to the link.\n   */\n  public static getRealPath(linkPath: string): string {\n    return FileSystem._wrapException(() => {\n      return fsx.realpathSync(linkPath);\n    });\n  }\n\n  /**\n   * An async version of {@link FileSystem.getRealPath}.\n   */\n  public static async getRealPathAsync(linkPath: string): Promise<string> {\n    return await FileSystem._wrapExceptionAsync(() => {\n      return fsx.realpath(linkPath);\n    });\n  }\n\n  // ===============\n  // UTILITY FUNCTIONS\n  // ===============\n\n  /**\n   * Returns true if the error object indicates the file or folder already exists (`EEXIST`).\n   */\n  public static isExistError(error: Error): boolean {\n    return FileSystem.isErrnoException(error) && error.code === 'EEXIST';\n  }\n\n  /**\n   * Returns true if the error object indicates the file or folder does not exist (`ENOENT` or `ENOTDIR`)\n   */\n  public static isNotExistError(error: Error): boolean {\n    return FileSystem.isFileDoesNotExistError(error) || FileSystem.isFolderDoesNotExistError(error);\n  }\n\n  /**\n   * Returns true if the error object indicates the file does not exist (`ENOENT`).\n   */\n  public static isFileDoesNotExistError(error: Error): boolean {\n    return FileSystem.isErrnoException(error) && error.code === 'ENOENT';\n  }\n\n  /**\n   * Returns true if the error object indicates the folder does not exist (`ENOTDIR`).\n   */\n  public static isFolderDoesNotExistError(error: Error): boolean {\n    return FileSystem.isErrnoException(error) && error.code === 'ENOTDIR';\n  }\n\n  /**\n   * Returns true if the error object indicates the target is a directory (`EISDIR`).\n   */\n  public static isDirectoryError(error: Error): boolean {\n    return FileSystem.isErrnoException(error) && error.code === 'EISDIR';\n  }\n\n  /**\n   * Returns true if the error object indicates the target is not a directory (`ENOTDIR`).\n   */\n  public static isNotDirectoryError(error: Error): boolean {\n    return FileSystem.isErrnoException(error) && error.code === 'ENOTDIR';\n  }\n\n  /**\n   * Returns true if the error object indicates that the `unlink` system call failed\n   * due to a permissions issue (`EPERM`).\n   */\n  public static isUnlinkNotPermittedError(error: Error): boolean {\n    return FileSystem.isErrnoException(error) && error.code === 'EPERM' && error.syscall === 'unlink';\n  }\n\n  /**\n   * Detects if the provided error object is a `NodeJS.ErrnoException`\n   */\n  public static isErrnoException(error: Error): error is NodeJS.ErrnoException {\n    const typedError: NodeJS.ErrnoException = error;\n    return (\n      typeof typedError.code === 'string' &&\n      typeof typedError.errno === 'number' &&\n      typeof typedError.path === 'string' &&\n      typeof typedError.syscall === 'string'\n    );\n  }\n\n  private static _handleLink(linkFn: () => void, options: IInternalFileSystemCreateLinkOptions): void {\n    try {\n      linkFn();\n    } catch (error) {\n      if (FileSystem.isExistError(error as Error)) {\n        // Link exists, handle it\n        switch (options.alreadyExistsBehavior) {\n          case AlreadyExistsBehavior.Ignore:\n            break;\n          case AlreadyExistsBehavior.Overwrite:\n            // fsx.linkSync does not allow overwriting so we must manually delete. If it's\n            // a folder, it will throw an error.\n            this.deleteFile(options.newLinkPath);\n            linkFn();\n            break;\n          case AlreadyExistsBehavior.Error:\n          default:\n            throw error;\n        }\n      } else {\n        // When attempting to create a link in a directory that does not exist, an ENOENT\n        // or ENOTDIR error is thrown, so we should ensure the directory exists before\n        // retrying. There are also cases where the target file must exist, so validate in\n        // those cases to avoid confusing the missing directory with the missing target file.\n        if (\n          FileSystem.isNotExistError(error as Error) &&\n          (!options.linkTargetMustExist || FileSystem.exists(options.linkTargetPath))\n        ) {\n          this.ensureFolder(nodeJsPath.dirname(options.newLinkPath));\n          linkFn();\n        } else {\n          throw error;\n        }\n      }\n    }\n  }\n\n  private static async _handleLinkAsync(\n    linkFn: () => Promise<void>,\n    options: IInternalFileSystemCreateLinkOptions\n  ): Promise<void> {\n    try {\n      await linkFn();\n    } catch (error) {\n      if (FileSystem.isExistError(error as Error)) {\n        // Link exists, handle it\n        switch (options.alreadyExistsBehavior) {\n          case AlreadyExistsBehavior.Ignore:\n            break;\n          case AlreadyExistsBehavior.Overwrite:\n            // fsx.linkSync does not allow overwriting so we must manually delete. If it's\n            // a folder, it will throw an error.\n            await this.deleteFileAsync(options.newLinkPath);\n            await linkFn();\n            break;\n          case AlreadyExistsBehavior.Error:\n          default:\n            throw error;\n        }\n      } else {\n        // When attempting to create a link in a directory that does not exist, an ENOENT\n        // or ENOTDIR error is thrown, so we should ensure the directory exists before\n        // retrying. There are also cases where the target file must exist, so validate in\n        // those cases to avoid confusing the missing directory with the missing target file.\n        if (\n          FileSystem.isNotExistError(error as Error) &&\n          (!options.linkTargetMustExist || (await FileSystem.existsAsync(options.linkTargetPath)))\n        ) {\n          await this.ensureFolderAsync(nodeJsPath.dirname(options.newLinkPath));\n          await linkFn();\n        } else {\n          throw error;\n        }\n      }\n    }\n  }\n\n  private static _wrapException<TResult>(fn: () => TResult): TResult {\n    try {\n      return fn();\n    } catch (error) {\n      FileSystem._updateErrorMessage(error as Error);\n      throw error;\n    }\n  }\n\n  private static async _wrapExceptionAsync<TResult>(fn: () => Promise<TResult>): Promise<TResult> {\n    try {\n      return await fn();\n    } catch (error) {\n      FileSystem._updateErrorMessage(error as Error);\n      throw error;\n    }\n  }\n\n  private static _updateErrorMessage(error: Error): void {\n    if (FileSystem.isErrnoException(error)) {\n      if (FileSystem.isFileDoesNotExistError(error)) {\n        // eslint-disable-line @typescript-eslint/no-use-before-define\n        error.message = `File does not exist: ${error.path}\\n${error.message}`;\n      } else if (FileSystem.isFolderDoesNotExistError(error)) {\n        // eslint-disable-line @typescript-eslint/no-use-before-define\n        error.message = `Folder does not exist: ${error.path}\\n${error.message}`;\n      } else if (FileSystem.isExistError(error)) {\n        // Oddly, the typing does not include the `dest` property even though the documentation\n        // indicates it is there: https://nodejs.org/docs/latest-v10.x/api/errors.html#errors_error_dest\n        const extendedError: NodeJS.ErrnoException & { dest?: string } = error;\n        // eslint-disable-line @typescript-eslint/no-use-before-define\n        error.message = `File or folder already exists: ${extendedError.dest}\\n${error.message}`;\n      } else if (FileSystem.isUnlinkNotPermittedError(error)) {\n        // eslint-disable-line @typescript-eslint/no-use-before-define\n        error.message = `File or folder could not be deleted: ${error.path}\\n${error.message}`;\n      } else if (FileSystem.isDirectoryError(error)) {\n        // eslint-disable-line @typescript-eslint/no-use-before-define\n        error.message = `Target is a folder, not a file: ${error.path}\\n${error.message}`;\n      } else if (FileSystem.isNotDirectoryError(error)) {\n        // eslint-disable-line @typescript-eslint/no-use-before-define\n        error.message = `Target is not a folder: ${error.path}\\n${error.message}`;\n      }\n    }\n  }\n}\n"]}