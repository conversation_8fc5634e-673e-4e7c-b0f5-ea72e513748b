{"version": 3, "file": "NormalizeNewlinesTextRewriter.js", "sourceRoot": "", "sources": ["../src/NormalizeNewlinesTextRewriter.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;;AAE3D,oEAAsE;AACtE,iDAAsE;AA6BtE;;;;;GAKG;AACH,MAAa,6BAA8B,SAAQ,2BAAY;IAY7D,YAAmB,OAA8C;QAC/D,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;QACvC,IAAI,CAAC,OAAO,GAAG,wBAAI,CAAC,UAAU,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;QACpD,IAAI,CAAC,kBAAkB,GAAG,CAAC,CAAC,OAAO,CAAC,kBAAkB,CAAC;IACzD,CAAC;IAEM,UAAU;QACf,OAAO;YACL,iBAAiB,EAAE,EAAE;YACrB,cAAc,EAAE,KAAK;SACiB,CAAC;IAC3C,CAAC;IAEM,OAAO,CAAC,YAA+B,EAAE,IAAY;QAC1D,MAAM,KAAK,GAAwC,YAAmD,CAAC;QAEvG,IAAI,MAAM,GAAW,EAAE,CAAC;QAExB,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACpB,IAAI,CAAC,GAAW,CAAC,CAAC;YAElB,GAAG,CAAC;gBACF,MAAM,CAAC,GAAW,IAAI,CAAC,CAAC,CAAC,CAAC;gBAC1B,EAAE,CAAC,CAAC;gBAEJ,IAAI,CAAC,KAAK,KAAK,CAAC,iBAAiB,EAAE,CAAC;oBAClC,KAAK,CAAC,iBAAiB,GAAG,EAAE,CAAC;gBAC/B,CAAC;qBAAM,IAAI,CAAC,KAAK,IAAI,EAAE,CAAC;oBACtB,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC;oBACvB,KAAK,CAAC,iBAAiB,GAAG,IAAI,CAAC;oBAC/B,KAAK,CAAC,cAAc,GAAG,KAAK,CAAC;gBAC/B,CAAC;qBAAM,IAAI,CAAC,KAAK,IAAI,EAAE,CAAC;oBACtB,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC;oBACvB,KAAK,CAAC,iBAAiB,GAAG,IAAI,CAAC;oBAC/B,KAAK,CAAC,cAAc,GAAG,KAAK,CAAC;gBAC/B,CAAC;qBAAM,CAAC;oBACN,MAAM,IAAI,CAAC,CAAC;oBACZ,KAAK,CAAC,iBAAiB,GAAG,EAAE,CAAC;oBAC7B,KAAK,CAAC,cAAc,GAAG,IAAI,CAAC;gBAC9B,CAAC;YACH,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE;QAC5B,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAEM,KAAK,CAAC,YAA+B;QAC1C,MAAM,KAAK,GAAwC,YAAmD,CAAC;QACvG,KAAK,CAAC,iBAAiB,GAAG,EAAE,CAAC;QAE7B,IAAI,KAAK,CAAC,cAAc,EAAE,CAAC;YACzB,KAAK,CAAC,cAAc,GAAG,KAAK,CAAC;YAC7B,OAAO,IAAI,CAAC,OAAO,CAAC;QACtB,CAAC;aAAM,CAAC;YACN,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;CACF;AAtED,sEAsEC", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\n// See LICEN<PERSON> in the project root for license information.\n\nimport { Text, type NewlineKind } from '@rushstack/node-core-library';\nimport { TextRewriter, type TextRewriterState } from './TextRewriter';\n\ninterface INormalizeNewlinesTextRewriterState extends TextRewriterState {\n  characterToIgnore: string;\n  incompleteLine: boolean;\n}\n\n/**\n * Constructor options for {@link NormalizeNewlinesTextRewriter}\n *\n * @public\n */\nexport interface INormalizeNewlinesTextRewriterOptions {\n  /**\n   * Specifies how newlines should be represented in the output stream.\n   */\n  newlineKind: NewlineKind;\n\n  /**\n   * If `true`, then `NormalizeNewlinesTextRewriter.close()` will append a newline to\n   * the output if it ends with an incomplete line.\n   *\n   * @remarks\n   * If the output is an empty string, then a newline will NOT be appended,\n   * because writing an empty string does not produce an incomplete line.\n   */\n  ensureNewlineAtEnd?: boolean;\n}\n\n/**\n * For use with {@link TextRewriterTransform}, this rewriter converts all newlines to\n * a standard format.\n *\n * @public\n */\nexport class NormalizeNewlinesTextRewriter extends TextRewriter {\n  /** {@inheritDoc INormalizeNewlinesTextRewriterOptions.newlineKind} */\n  public readonly newlineKind: NewlineKind;\n\n  /**\n   * The specific character sequence that will be used when appending newlines.\n   */\n  public readonly newline: string;\n\n  /** {@inheritDoc INormalizeNewlinesTextRewriterOptions.ensureNewlineAtEnd} */\n  public readonly ensureNewlineAtEnd: boolean;\n\n  public constructor(options: INormalizeNewlinesTextRewriterOptions) {\n    super();\n    this.newlineKind = options.newlineKind;\n    this.newline = Text.getNewline(options.newlineKind);\n    this.ensureNewlineAtEnd = !!options.ensureNewlineAtEnd;\n  }\n\n  public initialize(): TextRewriterState {\n    return {\n      characterToIgnore: '',\n      incompleteLine: false\n    } as INormalizeNewlinesTextRewriterState;\n  }\n\n  public process(unknownState: TextRewriterState, text: string): string {\n    const state: INormalizeNewlinesTextRewriterState = unknownState as INormalizeNewlinesTextRewriterState;\n\n    let result: string = '';\n\n    if (text.length > 0) {\n      let i: number = 0;\n\n      do {\n        const c: string = text[i];\n        ++i;\n\n        if (c === state.characterToIgnore) {\n          state.characterToIgnore = '';\n        } else if (c === '\\r') {\n          result += this.newline;\n          state.characterToIgnore = '\\n';\n          state.incompleteLine = false;\n        } else if (c === '\\n') {\n          result += this.newline;\n          state.characterToIgnore = '\\r';\n          state.incompleteLine = false;\n        } else {\n          result += c;\n          state.characterToIgnore = '';\n          state.incompleteLine = true;\n        }\n      } while (i < text.length);\n    }\n\n    return result;\n  }\n\n  public close(unknownState: TextRewriterState): string {\n    const state: INormalizeNewlinesTextRewriterState = unknownState as INormalizeNewlinesTextRewriterState;\n    state.characterToIgnore = '';\n\n    if (state.incompleteLine) {\n      state.incompleteLine = false;\n      return this.newline;\n    } else {\n      return '';\n    }\n  }\n}\n"]}