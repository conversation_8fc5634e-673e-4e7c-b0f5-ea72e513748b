{"version": 3, "file": "CommandLineParser.js", "sourceRoot": "", "sources": ["../../src/providers/CommandLineParser.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;;AAG3D,kDAA+C;AAI/C,iFAIwC;AACxC,6EAAgG;AAChG,+DAA0D;AAC1D,kDAAqE;AA6BrE;;;;;;;;;GASG;AACH,MAAsB,iBAAkB,SAAQ,2DAA4B;IAgB1E,YAAmB,OAAkC;;QACnD,KAAK,EAAE,CAAC;QAJF,cAAS,GAAY,KAAK,CAAC;QAC3B,+BAA0B,GAAY,KAAK,CAAC;QAKlD,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QACxB,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;QACnB,IAAI,CAAC,cAAc,GAAG,IAAI,GAAG,EAA6B,CAAC;QAE3D,IAAI,CAAC,eAAe,GAAG,IAAI,iDAAoB,CAAC;YAC9C,OAAO,EAAE,IAAI;YACb,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,YAAY;YAChC,WAAW,EAAE,IAAI,CAAC,QAAQ,CAAC,eAAe;YAC1C,MAAM,EAAE,mBAAQ,CAAC,IAAI,CACnB,MAAA,IAAI,CAAC,QAAQ,CAAC,UAAU,mCACtB,oDAAoD,IAAI,CAAC,QAAQ,CAAC,YAAY,eAAe,CAChG;SACF,CAAC,CAAC;QAEH,MAAA,IAAI,CAAC,kBAAkB,oDAAI,CAAC;IAC9B,CAAC;IAED;;OAEG;IACH,IAAW,OAAO;QAChB,OAAO,IAAI,CAAC,QAAQ,CAAC;IACvB,CAAC;IAED;;OAEG;IACI,SAAS,CAAC,MAAyB;QACxC,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAC5B,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC;gBAC1D,OAAO,EAAE,WAAW;gBACpB,IAAI,EAAE,QAAQ;aACf,CAAC,CAAC;QACL,CAAC;QAED,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAC5C,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC3B,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,MAAM,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;IACrD,CAAC;IAED;;;OAGG;IACI,SAAS,CAAC,UAAkB;QACjC,MAAM,MAAM,GAAkC,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;QAC5E,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,MAAM,IAAI,KAAK,CAAC,eAAe,UAAU,mBAAmB,CAAC,CAAC;QAChE,CAAC;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;;OAGG;IACI,YAAY,CAAC,UAAkB;QACpC,OAAO,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;IAC7C,CAAC;IAED;;;;;;;;;;;;;;;;OAgBG;IACI,KAAK,CAAC,YAAY,CAAC,IAAe;QACvC,IAAI,IAAI,CAAC,QAAQ,CAAC,yBAAyB,IAAI,CAAC,IAAI,CAAC,0BAA0B,EAAE,CAAC;YAChF,IAAI,CAAC,SAAS,CAAC,IAAI,uCAAiB,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC;YACrE,IAAI,CAAC,0BAA0B,GAAG,IAAI,CAAC;QACzC,CAAC;QAED,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,gCAAgC,CAAC,IAAI,CAAC,CAAC;YAClD,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACb,IAAI,GAAG,YAAY,uDAA0B,EAAE,CAAC;gBAC9C,mEAAmE;gBACnE,oDAAoD;gBACpD,IAAI,GAAG,CAAC,OAAO,EAAE,CAAC;oBAChB,sCAAsC;oBACtC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;gBAC7B,CAAC;gBACD,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;oBACtB,OAAO,CAAC,QAAQ,GAAG,GAAG,CAAC,QAAQ,CAAC;gBAClC,CAAC;YACH,CAAC;iBAAM,IAAI,uBAAQ,CAAC,YAAY,CAAC,GAAG,EAAE,uCAAwB,CAAC,EAAE,CAAC;gBAChE,wBAAwB;gBACxB,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;oBACtB,OAAO,CAAC,QAAQ,GAAG,CAAC,CAAC;gBACvB,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,IAAI,OAAO,GAAW,CAAE,GAAa,CAAC,OAAO,IAAI,2BAA2B,CAAC,CAAC,IAAI,EAAE,CAAC;gBAErF,uEAAuE;gBACvE,IAAI,CAAC,oCAAoC,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;oBACxD,OAAO,GAAG,SAAS,GAAG,OAAO,CAAC;gBAChC,CAAC;gBAED,sCAAsC;gBACtC,OAAO,CAAC,KAAK,EAAE,CAAC;gBAChB,sCAAsC;gBACtC,OAAO,CAAC,KAAK,CAAC,mBAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC;gBAErC,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;oBACtB,OAAO,CAAC,QAAQ,GAAG,CAAC,CAAC;gBACvB,CAAC;YACH,CAAC;YAED,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;OAEG;IACH,gEAAgE;IACzD,KAAK,CAAC,OAAO,CAAC,IAAe;QAClC,OAAO,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;IACvC,CAAC;IAED;;;OAGG;IACI,KAAK,CAAC,gCAAgC,CAAC,IAAe;;QAC3D,IAAI,CAAC;YACH,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;gBACnB,4EAA4E;gBAC5E,8EAA8E;gBAC9E,4BAA4B;gBAC5B,MAAM,IAAI,KAAK,CAAC,4DAA4D,CAAC,CAAC;YAChF,CAAC;YACD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;YAEtB,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAE5B,gEAAgE;YAChE,MAAM,YAAY,GAAoC;gBACpD,oBAAoB,EAAE,IAAI,GAAG,EAAE;aAChC,CAAC;YACF,IAAI,CAAC,0BAA0B,CAAC,YAAY,CAAC,CAAC;YAE9C,IAAI,CAAC,IAAI,EAAE,CAAC;gBACV,4BAA4B;gBAC5B,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YAC/B,CAAC;YACD,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC5B,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBACtB,6EAA6E;oBAC7E,+CAA+C;oBAC/C,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,CAAC;oBACjC,OAAO;gBACT,CAAC;gBACD,mFAAmF;gBACnF,kFAAkF;gBAClF,oFAAoF;gBACpF,kFAAkF;gBAClF,uBAAuB;gBACvB,MAAM,eAAe,GAAuB,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC;gBACtF,IAAI,eAAe,KAAK,SAAS,EAAE,CAAC;oBAClC,MAAM,UAAU,GAAW,IAAI,CAAC,eAAe,CAAC,CAAC;oBACjD,MAAM,MAAM,GAAkC,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;oBAC5E,MAAM,WAAW,GAAuC,MAAgC,CAAC;oBACzF,IAAI,MAAA,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAE,iBAAiB,0CAAE,MAAM,EAAE,CAAC;wBAC3C,MAAM,WAAW,GAAW,eAAe,GAAG,CAAC,CAAC;wBAChD,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,MAAM,CAAC,WAAW,CAAC,iBAAiB,EAAE,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC;oBACnG,CAAC;gBACH,CAAC;YACH,CAAC;YAED,MAAM,SAAS,GAAe,GAAG,EAAE;gBACjC,IAAI,CAAC,UAAU,EAAE,CAAC;gBAClB,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;oBAClC,MAAM,CAAC,UAAU,EAAE,CAAC;gBACtB,CAAC;YACH,CAAC,CAAC;YAEF,SAAS,iCAAiC,CAAC,cAAuC;gBAChF,MAAM,mBAAmB,GAAiB,cAAc,CAAC,WAAW,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;gBAC1F,cAAc,CAAC,WAAW,GAAG,GAAG,EAAE;oBAChC,SAAS,EAAE,CAAC;oBACZ,OAAO,mBAAmB,EAAE,CAAC;gBAC/B,CAAC,CAAC;YACJ,CAAC;YAED,IAAI,CAAC,SAAS,EAAE,CAAC;YACjB,iCAAiC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YACxD,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;gBAClC,MAAM,CAAC,SAAS,EAAE,CAAC;gBACnB,iCAAiC,CAAC,MAAM,CAAC,kBAAkB,EAAE,CAAC,CAAC;YACjE,CAAC;YAED,MAAM,IAAI,GAA2B,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;YAE1E,SAAS,EAAE,CAAC;YACZ,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;YAE7C,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACrD,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;gBACpD,MAAM,OAAO,GAAa,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC;gBAChE,MAAM,IAAI,KAAK,CAAC,gCAAgC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACzE,CAAC;YAED,MAAA,IAAI,CAAC,cAAc,0CAAE,kBAAkB,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;YAC7D,MAAM,IAAI,CAAC,SAAS,EAAE,CAAC;QACzB,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACb,IAAI,GAAG,YAAY,uDAA0B,EAAE,CAAC;gBAC9C,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC;oBAClB,kDAAkD;oBAClD,IAAI,GAAG,CAAC,OAAO,EAAE,CAAC;wBAChB,sCAAsC;wBACtC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;oBAC3B,CAAC;oBAED,OAAO;gBACT,CAAC;YACH,CAAC;YAED,MAAM,GAAG,CAAC;QACZ,CAAC;IACH,CAAC;IAED;;OAEG;IACH,gEAAgE;IACzD,KAAK,CAAC,2BAA2B,CAAC,IAAe;QACtD,MAAM,IAAI,CAAC,gCAAgC,CAAC,IAAI,CAAC,CAAC;IACpD,CAAC;IAED,gBAAgB;IACT,0BAA0B,CAAC,KAAsC;QACtE,KAAK,CAAC,0BAA0B,CAAC,KAAK,CAAC,CAAC;QAExC,MAAM,EAAE,oBAAoB,EAAE,GAAG,KAAK,CAAC;QACvC,MAAM,2BAA2B,GAAgB,IAAI,GAAG,CAAC;YACvD,GAAG,oBAAoB;YACvB,GAAG,IAAI,CAAC,oCAAoC,CAAC,IAAI,EAAE;SACpD,CAAC,CAAC;QAEH,MAAM,WAAW,GAAoC;YACnD,GAAG,KAAK;YACR,oBAAoB,EAAE,2BAA2B;SAClD,CAAC;QACF,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YACnC,MAAM,CAAC,0BAA0B,CAAC,WAAW,CAAC,CAAC;QACjD,CAAC;IACH,CAAC;IAEO,oBAAoB;QAC1B,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC9C,+CAA+C;YAC/C,MAAM,IAAI,KAAK,CAAC,oFAAoF,CAAC,CAAC;QACxG,CAAC;IACH,CAAC;IAED;;;OAGG;IACO,kBAAkB;QAC1B,WAAW;QACX,OAAO,IAAI,CAAC,eAAe,CAAC;IAC9B,CAAC;IAED;;;OAGG;IACO,KAAK,CAAC,SAAS;QACvB,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACxB,MAAM,IAAI,CAAC,cAAc,CAAC,aAAa,EAAE,CAAC;QAC5C,CAAC;IACH,CAAC;CACF;AAjTD,8CAiTC", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\n// See L<PERSON>EN<PERSON> in the project root for license information.\n\nimport type * as argparse from 'argparse';\nimport { Colorize } from '@rushstack/terminal';\n\nimport type { CommandLineAction } from './CommandLineAction';\nimport type { AliasCommandLineAction } from './AliasCommandLineAction';\nimport {\n  CommandLineParameterProvider,\n  type IRegisterDefinedParametersState,\n  type ICommandLineParserData\n} from './CommandLineParameterProvider';\nimport { CommandLineParserExitError, CustomArgumentParser } from './CommandLineParserExitError';\nimport { TabCompleteAction } from './TabCompletionAction';\nimport { TypeUuid, uuidAlreadyReportedError } from '../TypeUuidLite';\n\n/**\n * Options for the {@link CommandLineParser} constructor.\n * @public\n */\nexport interface ICommandLineParserOptions {\n  /**\n   * The name of your tool when invoked from the command line\n   */\n  toolFilename: string;\n\n  /**\n   * General documentation that is included in the \"--help\" main page\n   */\n  toolDescription: string;\n\n  /**\n   * An optional string to append at the end of the \"--help\" main page. If not provided, an epilog\n   * will be automatically generated based on the toolFilename.\n   */\n  toolEpilog?: string;\n\n  /**\n   * Set to true to auto-define a tab completion action. False by default.\n   */\n  enableTabCompletionAction?: boolean;\n}\n\n/**\n * The \"argparse\" library is a relatively advanced command-line parser with features such\n * as word-wrapping and intelligible error messages (that are lacking in other similar\n * libraries such as commander, yargs, and nomnom).  Unfortunately, its ruby-inspired API\n * is awkward to use.  The abstract base classes CommandLineParser and CommandLineAction\n * provide a wrapper for \"argparse\" that makes defining and consuming arguments quick\n * and simple, and enforces that appropriate documentation is provided for each parameter.\n *\n * @public\n */\nexport abstract class CommandLineParser extends CommandLineParameterProvider {\n  /**\n   * Reports which CommandLineAction was specified on the command line.\n   * @remarks\n   * The value will be assigned before onExecute() is invoked.\n   */\n  public selectedAction: CommandLineAction | undefined;\n\n  private readonly _argumentParser: argparse.ArgumentParser;\n  private _actionsSubParser: argparse.SubParser | undefined;\n  private readonly _options: ICommandLineParserOptions;\n  private readonly _actions: CommandLineAction[];\n  private readonly _actionsByName: Map<string, CommandLineAction>;\n  private _executed: boolean = false;\n  private _tabCompleteActionWasAdded: boolean = false;\n\n  public constructor(options: ICommandLineParserOptions) {\n    super();\n\n    this._options = options;\n    this._actions = [];\n    this._actionsByName = new Map<string, CommandLineAction>();\n\n    this._argumentParser = new CustomArgumentParser({\n      addHelp: true,\n      prog: this._options.toolFilename,\n      description: this._options.toolDescription,\n      epilog: Colorize.bold(\n        this._options.toolEpilog ??\n          `For detailed help about a specific command, use: ${this._options.toolFilename} <command> -h`\n      )\n    });\n\n    this.onDefineParameters?.();\n  }\n\n  /**\n   * Returns the list of actions that were defined for this CommandLineParser object.\n   */\n  public get actions(): ReadonlyArray<CommandLineAction> {\n    return this._actions;\n  }\n\n  /**\n   * Defines a new action that can be used with the CommandLineParser instance.\n   */\n  public addAction(action: CommandLineAction): void {\n    if (!this._actionsSubParser) {\n      this._actionsSubParser = this._argumentParser.addSubparsers({\n        metavar: '<command>',\n        dest: 'action'\n      });\n    }\n\n    action._buildParser(this._actionsSubParser);\n    this._actions.push(action);\n    this._actionsByName.set(action.actionName, action);\n  }\n\n  /**\n   * Retrieves the action with the specified name.  If no matching action is found,\n   * an exception is thrown.\n   */\n  public getAction(actionName: string): CommandLineAction {\n    const action: CommandLineAction | undefined = this.tryGetAction(actionName);\n    if (!action) {\n      throw new Error(`The action \"${actionName}\" was not defined`);\n    }\n    return action;\n  }\n\n  /**\n   * Retrieves the action with the specified name.  If no matching action is found,\n   * undefined is returned.\n   */\n  public tryGetAction(actionName: string): CommandLineAction | undefined {\n    return this._actionsByName.get(actionName);\n  }\n\n  /**\n   * The program entry point will call this method to begin parsing command-line arguments\n   * and executing the corresponding action.\n   *\n   * @remarks\n   * The returned promise will never reject:  If an error occurs, it will be printed\n   * to stderr, process.exitCode will be set to 1, and the promise will resolve to false.\n   * This simplifies the most common usage scenario where the program entry point doesn't\n   * want to be involved with the command-line logic, and will discard the promise without\n   * a then() or catch() block.\n   *\n   * If your caller wants to trap and handle errors, use {@link CommandLineParser.executeWithoutErrorHandlingAsync}\n   * instead.\n   *\n   * @param args - the command-line arguments to be parsed; if omitted, then\n   *               the process.argv will be used\n   */\n  public async executeAsync(args?: string[]): Promise<boolean> {\n    if (this._options.enableTabCompletionAction && !this._tabCompleteActionWasAdded) {\n      this.addAction(new TabCompleteAction(this.actions, this.parameters));\n      this._tabCompleteActionWasAdded = true;\n    }\n\n    try {\n      await this.executeWithoutErrorHandlingAsync(args);\n      return true;\n    } catch (err) {\n      if (err instanceof CommandLineParserExitError) {\n        // executeWithoutErrorHandlingAsync() handles the successful cases,\n        // so here we can assume err has a nonzero exit code\n        if (err.message) {\n          // eslint-disable-next-line no-console\n          console.error(err.message);\n        }\n        if (!process.exitCode) {\n          process.exitCode = err.exitCode;\n        }\n      } else if (TypeUuid.isInstanceOf(err, uuidAlreadyReportedError)) {\n        //  AlreadyReportedError\n        if (!process.exitCode) {\n          process.exitCode = 1;\n        }\n      } else {\n        let message: string = ((err as Error).message || 'An unknown error occurred').trim();\n\n        // If the message doesn't already start with \"Error:\" then add a prefix\n        if (!/^(error|internal error|warning)\\b/i.test(message)) {\n          message = 'Error: ' + message;\n        }\n\n        // eslint-disable-next-line no-console\n        console.error();\n        // eslint-disable-next-line no-console\n        console.error(Colorize.red(message));\n\n        if (!process.exitCode) {\n          process.exitCode = 1;\n        }\n      }\n\n      return false;\n    }\n  }\n\n  /**\n   * @deprecated Use {@link CommandLineParser.executeAsync} instead.\n   */\n  // eslint-disable-next-line @typescript-eslint/naming-convention\n  public async execute(args?: string[]): Promise<boolean> {\n    return await this.executeAsync(args);\n  }\n\n  /**\n   * This is similar to {@link CommandLineParser.executeAsync}, except that execution errors\n   * simply cause the promise to reject.  It is the caller's responsibility to trap\n   */\n  public async executeWithoutErrorHandlingAsync(args?: string[]): Promise<void> {\n    try {\n      if (this._executed) {\n        // In the future we could allow the same parser to be invoked multiple times\n        // with different arguments.  We'll do that work as soon as someone encounters\n        // a real world need for it.\n        throw new Error('executeAsync() was already called for this parser instance');\n      }\n      this._executed = true;\n\n      this._validateDefinitions();\n\n      // Register the parameters before we print help or parse the CLI\n      const initialState: IRegisterDefinedParametersState = {\n        parentParameterNames: new Set()\n      };\n      this._registerDefinedParameters(initialState);\n\n      if (!args) {\n        // 0=node.exe, 1=script name\n        args = process.argv.slice(2);\n      }\n      if (this.actions.length > 0) {\n        if (args.length === 0) {\n          // Parsers that use actions should print help when 0 args are provided. Allow\n          // actionless parsers to continue on zero args.\n          this._argumentParser.printHelp();\n          return;\n        }\n        // Alias actions may provide a list of default params to add after the action name.\n        // Since we don't know which params are required and which are optional, perform a\n        // manual search for the action name to obtain the default params and insert them if\n        // any are found. We will guess that the action name is the first arg that doesn't\n        // start with a hyphen.\n        const actionNameIndex: number | undefined = args.findIndex((x) => !x.startsWith('-'));\n        if (actionNameIndex !== undefined) {\n          const actionName: string = args[actionNameIndex];\n          const action: CommandLineAction | undefined = this.tryGetAction(actionName);\n          const aliasAction: AliasCommandLineAction | undefined = action as AliasCommandLineAction;\n          if (aliasAction?.defaultParameters?.length) {\n            const insertIndex: number = actionNameIndex + 1;\n            args = args.slice(0, insertIndex).concat(aliasAction.defaultParameters, args.slice(insertIndex));\n          }\n        }\n      }\n\n      const postParse: () => void = () => {\n        this._postParse();\n        for (const action of this.actions) {\n          action._postParse();\n        }\n      };\n\n      function patchFormatUsageForArgumentParser(argumentParser: argparse.ArgumentParser): void {\n        const originalFormatUsage: () => string = argumentParser.formatUsage.bind(argumentParser);\n        argumentParser.formatUsage = () => {\n          postParse();\n          return originalFormatUsage();\n        };\n      }\n\n      this._preParse();\n      patchFormatUsageForArgumentParser(this._argumentParser);\n      for (const action of this.actions) {\n        action._preParse();\n        patchFormatUsageForArgumentParser(action._getArgumentParser());\n      }\n\n      const data: ICommandLineParserData = this._argumentParser.parseArgs(args);\n\n      postParse();\n      this._processParsedData(this._options, data);\n\n      this.selectedAction = this.tryGetAction(data.action);\n      if (this.actions.length > 0 && !this.selectedAction) {\n        const actions: string[] = this.actions.map((x) => x.actionName);\n        throw new Error(`An action must be specified (${actions.join(', ')})`);\n      }\n\n      this.selectedAction?._processParsedData(this._options, data);\n      await this.onExecute();\n    } catch (err) {\n      if (err instanceof CommandLineParserExitError) {\n        if (!err.exitCode) {\n          // non-error exit modeled using exception handling\n          if (err.message) {\n            // eslint-disable-next-line no-console\n            console.log(err.message);\n          }\n\n          return;\n        }\n      }\n\n      throw err;\n    }\n  }\n\n  /**\n   * @deprecated Use {@link CommandLineParser.executeWithoutErrorHandlingAsync} instead.\n   */\n  // eslint-disable-next-line @typescript-eslint/naming-convention\n  public async executeWithoutErrorHandling(args?: string[]): Promise<void> {\n    await this.executeWithoutErrorHandlingAsync(args);\n  }\n\n  /** @internal */\n  public _registerDefinedParameters(state: IRegisterDefinedParametersState): void {\n    super._registerDefinedParameters(state);\n\n    const { parentParameterNames } = state;\n    const updatedParentParameterNames: Set<string> = new Set([\n      ...parentParameterNames,\n      ...this._registeredParameterParserKeysByName.keys()\n    ]);\n\n    const parentState: IRegisterDefinedParametersState = {\n      ...state,\n      parentParameterNames: updatedParentParameterNames\n    };\n    for (const action of this._actions) {\n      action._registerDefinedParameters(parentState);\n    }\n  }\n\n  private _validateDefinitions(): void {\n    if (this.remainder && this.actions.length > 0) {\n      // This is apparently not supported by argparse\n      throw new Error('defineCommandLineRemainder() cannot be called for a CommandLineParser with actions');\n    }\n  }\n\n  /**\n   * {@inheritDoc CommandLineParameterProvider._getArgumentParser}\n   * @internal\n   */\n  protected _getArgumentParser(): argparse.ArgumentParser {\n    // override\n    return this._argumentParser;\n  }\n\n  /**\n   * This hook allows the subclass to perform additional operations before or after\n   * the chosen action is executed.\n   */\n  protected async onExecute(): Promise<void> {\n    if (this.selectedAction) {\n      await this.selectedAction._executeAsync();\n    }\n  }\n}\n"]}