{"version": 3, "file": "detailed-log.mjs", "sources": ["../../src/formats/detailed-log.ts"], "sourcesContent": ["import { format } from 'winston';\n\n/**\n * This will remove the chalk color codes from the message provided.\n * It's used to log plain text in the log file\n */\nexport default format.printf(({ message, level, timestamp }) => {\n  if (typeof message !== 'string') {\n    return message;\n  }\n\n  const newMessage = `[${timestamp as string}] ${level}: ${message as string}`;\n\n  return newMessage.replace(\n    // eslint-disable-next-line no-control-regex\n    /[\\u001b\\u009b][[()#;?]*(?:[0-9]{1,4}(?:;[0-9]{0,4})*)?[0-9A-ORZcf-nqry=><]/g,\n    ''\n  );\n});\n"], "names": ["format", "printf", "message", "level", "timestamp", "newMessage", "replace"], "mappings": ";;AAEA;;;IAIA,kBAAeA,MAAOC,CAAAA,MAAM,CAAC,CAAC,EAAEC,OAAO,EAAEC,KAAK,EAAEC,SAAS,EAAE,GAAA;IACzD,IAAI,OAAOF,YAAY,QAAU,EAAA;QAC/B,OAAOA,OAAAA;AACT;IAEA,MAAMG,UAAAA,GAAa,CAAC,CAAC,EAAED,SAAAA,CAAoB,EAAE,EAAED,KAAM,CAAA,EAAE,EAAED,OAAAA,CAAkB,CAAC;IAE5E,OAAOG,UAAAA,CAAWC,OAAO;IAEvB,6EACA,EAAA,EAAA,CAAA;AAEJ,CAAG,CAAA;;;;"}